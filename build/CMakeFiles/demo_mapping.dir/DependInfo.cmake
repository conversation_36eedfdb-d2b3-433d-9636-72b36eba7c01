# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/桌面/RoadLib-master/camodocal/camera_models/Camera.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/camera_models/Camera.cc.o"
  "/home/<USER>/桌面/RoadLib-master/camodocal/camera_models/CameraFactory.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/camera_models/CameraFactory.cc.o"
  "/home/<USER>/桌面/RoadLib-master/camodocal/camera_models/CataCamera.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/camera_models/CataCamera.cc.o"
  "/home/<USER>/桌面/RoadLib-master/camodocal/camera_models/EquidistantCamera.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/camera_models/EquidistantCamera.cc.o"
  "/home/<USER>/桌面/RoadLib-master/camodocal/camera_models/PinholeCamera.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/camera_models/PinholeCamera.cc.o"
  "/home/<USER>/桌面/RoadLib-master/camodocal/camera_models/PinholeFullCamera.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/camera_models/PinholeFullCamera.cc.o"
  "/home/<USER>/桌面/RoadLib-master/camodocal/camera_models/ScaramuzzaCamera.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/camera_models/ScaramuzzaCamera.cc.o"
  "/home/<USER>/桌面/RoadLib-master/camodocal/gpl/gpl.cc" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/camodocal/gpl/gpl.cc.o"
  "/home/<USER>/桌面/RoadLib-master/demo/demo_mapping.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/demo/demo_mapping.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/demo/main_phase_mapping.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/demo/main_phase_mapping.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/gv_tools/gv_utils.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/gv_tools/gv_utils.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/gv_tools/ipm_processer.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/gv_tools/ipm_processer.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/roadlib/gviewer.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/roadlib/gviewer.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/roadlib/roadlib.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/roadlib/roadlib.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/roadlib/roadlib_map.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/roadlib/roadlib_map.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/roadlib/roadlib_optim.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/roadlib/roadlib_optim.cpp.o"
  "/home/<USER>/桌面/RoadLib-master/roadlib/visualization.cpp" "/home/<USER>/桌面/RoadLib-master/build/CMakeFiles/demo_mapping.dir/roadlib/visualization.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "DISABLE_LIBUSB_1_0"
  "DISABLE_PCAP"
  "DISABLE_PNG"
  "ROOT_DIR=\"/home/<USER>/桌面/RoadLib-master/\""
  "qh_QHpointer"
  "vtkRenderingContext2D_AUTOINIT=1(vtkRenderingContextOpenGL2)"
  "vtkRenderingCore_AUTOINIT=3(vtkInteractionStyle,vtkRenderingFreeType,vtkRenderingOpenGL2)"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "/usr/local/include/eigen3"
  "../."
  ".././gv_tools"
  ".././roadlib"
  "/usr/include/vtk-7.1"
  "/usr/include/freetype2"
  "/usr/include/pcl-1.10"
  "/usr/include/eigen3"
  "/usr/include/ni"
  "/usr/include/openni2"
  "/usr/local/include/opencv"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
