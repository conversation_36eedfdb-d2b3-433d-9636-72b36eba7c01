/*******************************************************
 * Copyright (C) 2024, GREAT Group, Wuhan University
 *
 * This file is part of RoadLib.
 *
 * Licensed under the GNU General Public License v3.0;
 * you may not use this file except in compliance with the License.
 *
 * Author: <PERSON><PERSON><PERSON> (<EMAIL>)
 *******************************************************/
#include "roadlib.h"
#include <pcl/point_cloud.h>
#include <pcl/kdtree/kdtree_flann.h>

// === 全局变量：用于地图匹配的KD树数据结构 ===
map<PatchType, pcl::PointCloud<pcl::PointXYZ>::Ptr> class_pts;      // 按类型分组的点云数据
map<PatchType, vector<pair<int, int>>> class_pts_index;              // 点云索引映射 <类型, <点索引, 实例索引>>
map<PatchType, pcl::KdTreeFLANN<pcl::PointXYZ>> class_kdtree;       // 按类型分组的KD树

/**
 * @brief 向地图中添加一帧道路标记数据
 * 将帧中的有效道路标记实例转换到世界坐标系并添加到地图中
 * @param frame 输入的道路标记帧
 * @return 成功返回0
 */
int RoadInstancePatchMap::addFrame(const RoadInstancePatchFrame& frame)
{
	// 遍历帧中的所有道路标记类型
	for (auto iter_class = frame.patches.begin(); iter_class != frame.patches.end(); iter_class++)
	{
		// 遍历当前类型的所有实例
		for (auto iter_instance = iter_class->second.begin(); iter_instance != iter_class->second.end(); iter_instance++)
		{
			// 只处理有效的实例(通过了质量检查的)
			if ((*iter_instance)->valid_add_to_map)
			{
				// 在地图中创建新的实例
				patches[iter_class->first].push_back(make_shared<RoadInstancePatch>(RoadInstancePatch()));
				auto& patch = patches[iter_class->first].back();

				// 复制基本属性
				patch->frame_id = frame.id;
				patch->road_class = (*iter_instance)->road_class;
				patch->valid_add_to_map = true;
				patch->line_valid = (*iter_instance)->line_valid;
				patch->percept_distance = (*iter_instance)->percept_distance;

				// 将质心从车体坐标系转换到世界坐标系: P_world = t + R * P_body
				patch->mean_metric = frame.t + frame.R * (*iter_instance)->mean_metric;

				// 将不确定性矩阵从车体坐标系转换到世界坐标系: Cov_world = R * Cov_body * R^T
				patch->mean_uncertainty = frame.R * (*iter_instance)->mean_uncertainty * frame.R.transpose();
				patch->line_points_uncertainty = (*iter_instance)->line_points_uncertainty;

				// 如果是线段类型，转换所有线段点
				if ((*iter_instance)->line_valid)
				{
					patch->line_points_metric.resize((*iter_instance)->line_points_metric.size());
					patch->line_points_uncertainty.resize((*iter_instance)->line_points_metric.size());
					for (int j = 0; j < patch->line_points_metric.size(); j++)
					{
						// 将线段点从车体坐标系转换到世界坐标系
						patch->line_points_metric[j] = frame.t + frame.R * (*iter_instance)->line_points_metric[j];
						// 转换不确定性矩阵
						patch->line_points_uncertainty[j] = frame.R * patch->line_points_uncertainty[j] * frame.R.transpose();
					}
				}

				// 转换边界框的四个顶点
				for (int ii = 0; ii < 4; ii++)
				{
					patch->b_point_metric[ii] = frame.t + frame.R * (*iter_instance)->b_point_metric[ii];
					patch->b_unc_dist[ii] = (*iter_instance)->b_unc_dist[ii];
				}
			}
		}
	}

	// 缓存当前帧的位姿信息，用于后续的地理配准
	queued_poses[frame.id] = make_pair(frame.R, frame.t);
	timestamps[frame.id] = frame.time;
	return 0;
}

/**
 * @brief 清空地图中的所有道路标记实例
 * @return 成功返回0
 */
int RoadInstancePatchMap::clearMap()
{
	patches.clear();  // 清空所有类型的道路标记实例
	return 0;
}

/**
 * @brief 将地图保存为二进制文件
 * 保存格式：参考点 + 类别数量 + 各类别数据(类别ID + 实例数量 + 实例数据)
 * @param filename 输出文件名
 * @return 成功返回0
 */
int RoadInstancePatchMap::saveMapToFileBinaryRaw(string filename)
{
	std::cout<<"[INFO] Saving map to "<<filename<<"..."<<std::endl;
	size_t count;
	float float_buffer[9];  // 临时缓冲区，用于类型转换
	FILE* fp = fopen(filename.c_str(), "wb");

	// 1. 写入地图参考点(3个double值)
	fwrite(ref.data(), sizeof(double), 3, fp);

	// 2. 写入道路标记类别数量
	count = patches.size();
	fwrite(&count, sizeof(count), 1, fp);

	// 3. 遍历每个道路标记类别
	for (auto iter = patches.begin(); iter != patches.end(); iter++)
	{
		// 写入类别ID
		fwrite(&(iter->first), sizeof(iter->first), 1, fp);

		// 写入该类别的实例数量
		count = iter->second.size();
		fwrite(&(count), sizeof(count), 1, fp);

		// 4. 遍历该类别的每个实例
		for (int i = 0; i < iter->second.size(); i++)
		{
			auto& this_patch = iter->second[i];

			// 写入实例基本信息
			fwrite(&(RoadInstancePatchFrame::next_id), sizeof(RoadInstancePatchFrame::next_id), 1, fp);
			fwrite(&(this_patch->id), sizeof(this_patch->id), 1, fp);
			fwrite(&(this_patch->road_class), sizeof(this_patch->road_class), 1, fp);

			// 写入状态标志
			fwrite(&(this_patch->line_valid), sizeof(this_patch->line_valid), 1, fp);
			fwrite(&(this_patch->frozen), sizeof(this_patch->frozen), 1, fp);
			fwrite(&(this_patch->merged), sizeof(this_patch->merged), 1, fp);
			fwrite(&(this_patch->valid_add_to_map), sizeof(this_patch->valid_add_to_map), 1, fp);

			// 写入质心坐标(转换为float以节省空间)
			for (int z = 0; z < 3; z++) float_buffer[z] = (float)this_patch->mean_metric(z);
			fwrite(float_buffer, sizeof(float), 3, fp);

			// 写入边界框四个顶点坐标
			for (int iii = 0; iii < 4; iii++)
			{
				for (int z = 0; z < 3; z++) float_buffer[z] = (float)this_patch->b_point_metric[iii](z);
				fwrite(float_buffer, sizeof(float), 3, fp);
			}

			// 写入边界框不确定性
			for (int z = 0; z < 4; z++) float_buffer[z] = (float)this_patch->b_unc_dist[z];
			fwrite(float_buffer, sizeof(float), 4, fp);

			// 写入线段点数据
			count = this_patch->line_points_metric.size();
			fwrite(&(count), sizeof(count), 1, fp);
			for (int ii = 0; ii < this_patch->line_points_metric.size(); ii++)
			{
				// 写入线段点坐标
				for (int z = 0; z < 3; z++) float_buffer[z] = (float)this_patch->line_points_metric[ii](z);
				fwrite(float_buffer, sizeof(float), 3, fp);
			}

			// 写入质心不确定性矩阵(3x3矩阵，按行展开)
			for (int z = 0; z < 9; z++) float_buffer[z] = (float)this_patch->mean_uncertainty(z / 3, z % 3);
			fwrite(float_buffer, sizeof(float), 9, fp);

			// 写入线段点不确定性矩阵
			count = this_patch->line_points_uncertainty.size();
			fwrite(&(count), sizeof(count), 1, fp);
			for (int ii = 0; ii < this_patch->line_points_uncertainty.size(); ii++)
			{
				// 写入每个线段点的不确定性矩阵(3x3)
				for (int z = 0; z < 9; z++) float_buffer[z] = (float)this_patch->line_points_uncertainty[ii](z / 3, z % 3);
				fwrite(float_buffer, sizeof(float), 9, fp);
			}

			// 写入感知距离
			fwrite(&(this_patch->percept_distance), sizeof(this_patch->percept_distance), 1, fp);
		}
	}
	fclose(fp);  // 关闭文件
	std::cout<<"[INFO] Finished."<<std::endl;
	return 0;
}

/**
 * @brief 从二进制文件加载地图
 * 按照saveMapToFileBinaryRaw的格式读取地图数据
 * @param filename 输入文件名
 * @return 成功返回0
 */
int RoadInstancePatchMap::loadMapFromFileBinaryRaw(string filename)
{
	std::cout<<"[INFO] Loading map from "<<filename<<"..."<<std::endl;
	FILE* fp = fopen(filename.c_str(), "rb");

	// 1. 读取地图参考点
	for (int i = 0; i < 3; i++) fread(&ref(i), sizeof(double), 1, fp);

	// 2. 读取道路标记类别数量
	size_t class_count;
	fread(&class_count, sizeof(size_t), 1, fp);
	float float_temp[9];  // 临时缓冲区

	// 3. 遍历每个道路标记类别
	for (int i_class = 0; i_class < class_count; i_class++)
	{
		// 读取类别ID
		PatchType road_class;
		fread(&road_class, sizeof(int), 1, fp);

		// 读取该类别的实例数量
		size_t pcount;
		fread(&pcount, sizeof(size_t), 1, fp);
		patches[road_class];  // 初始化该类别的容器

		// 4. 遍历该类别的每个实例
		for (int i_patch = 0; i_patch < pcount; i_patch++)
		{
			RoadInstancePatch patch;

			// 读取实例基本信息
			fread(&patch.next_id, sizeof(patch.next_id), 1, fp);
			fread(&patch.id, sizeof(patch.id), 1, fp);
			fread(&patch.road_class, sizeof(patch.road_class), 1, fp);

			// 读取状态标志
			fread(&patch.line_valid, sizeof(patch.line_valid), 1, fp);
			fread(&(patch.frozen), sizeof(patch.frozen), 1, fp);
			fread(&(patch.merged), sizeof(patch.merged), 1, fp);
			fread(&patch.valid_add_to_map, sizeof(patch.valid_add_to_map), 1, fp);

			// 读取质心坐标(从float转换为double)
			fread(&float_temp[0], sizeof(float), 3, fp);
			for (int i = 0; i < 3; i++) patch.mean_metric(i) = float_temp[i];

			// 读取边界框四个顶点坐标
			for (int iii = 0; iii < 4; iii++)
			{
				fread(&float_temp[0], sizeof(float), 3, fp);
				for (int i = 0; i < 3; i++) patch.b_point_metric[iii](i) = float_temp[i];
			}

			// 读取边界框不确定性
			fread(&float_temp[0], sizeof(float), 4, fp);
			for (int iii = 0; iii < 4; iii++)
				patch.b_unc_dist[iii] = float_temp[iii];

			// 读取线段点数据
			size_t lpcount;
			fread(&lpcount, sizeof(lpcount), 1, fp);
			patch.line_points_metric.resize(lpcount);
			for (int lp = 0; lp < lpcount; lp++)
			{
				fread(&float_temp[0], sizeof(float), 3, fp);
				for (int i = 0; i < 3; i++) patch.line_points_metric[lp](i) = float_temp[i];
			}

			// 读取质心不确定性矩阵
			fread(&float_temp[0], sizeof(float), 9, fp);
			for (int i = 0; i < 9; i++) patch.mean_uncertainty(i / 3, i % 3) = float_temp[i];

			// 读取线段点不确定性矩阵
			fread(&lpcount, sizeof(lpcount), 1, fp);
			patch.line_points_uncertainty.resize(lpcount);
			for (int lp = 0; lp < lpcount; lp++)
			{
				fread(&float_temp[0], sizeof(float), 9, fp);
				for (int i = 0; i < 9; i++) patch.line_points_uncertainty[lp](i / 3, i % 3) = float_temp[i];
			}

			// 读取感知距离
			fread(&patch.percept_distance, sizeof(patch.percept_distance), 1, fp);

			// 将实例添加到地图中
			patches[road_class].push_back(make_shared<RoadInstancePatch>(patch));
		}
	}
	std::cout << "[INFO] Map successfully loaded." << std::endl;
	return 0;
}

/**
 * @brief 构建KD树用于快速地图匹配
 * 为每种道路标记类型构建独立的KD树，用于快速最近邻搜索
 * @return 成功返回0
 */
int RoadInstancePatchMap::buildKDTree()
{
	// 遍历地图中的每种道路标记类型
	for (auto iter_class = this->patches.begin(); iter_class != this->patches.end(); iter_class++)
	{
		// 为当前类型创建点云、索引映射和KD树
		class_pts.emplace(iter_class->first, pcl::PointCloud<pcl::PointXYZ>::Ptr(new pcl::PointCloud<pcl::PointXYZ>));
		class_pts_index.emplace(iter_class->first, vector<pair<int, int>>());
		class_kdtree.emplace(iter_class->first, pcl::KdTreeFLANN<pcl::PointXYZ>());

		auto& this_pts_ptr = class_pts[iter_class->first];      // 当前类型的点云
		auto& kdtree = class_kdtree[iter_class->first];         // 当前类型的KD树
		auto& this_pts_index = class_pts_index[iter_class->first]; // 当前类型的索引映射

		// 对于patch类型的道路标记(虚线、引导标记)：使用质心作为代表点
		if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
		{
			for (int i = 0; i < iter_class->second.size(); i++)
			{
				// 索引映射：<实例索引, 0> (0表示使用质心)
				this_pts_index.push_back(make_pair(i, 0));
			}
		}
		// 对于line类型的道路标记(实线、停止线)：使用所有线段点
		else if (iter_class->first == PatchType::SOLID || iter_class->first == PatchType::STOP)
		{
			for (int i = 0; i < iter_class->second.size(); i++)
			{
				for (int j = 0; j < iter_class->second[i]->line_points_metric.size(); j++)
				{
					// 索引映射：<实例索引, 线段点索引>
					this_pts_index.push_back(make_pair(i, j));
				}
			}
		}

		// 设置点云属性
		this_pts_ptr->width = this_pts_index.size();  // 点的数量
		this_pts_ptr->height = 1;                     // 单行点云
		this_pts_ptr->is_dense = false;               // 可能包含无效点
		this_pts_ptr->points.resize(this_pts_ptr->width * this_pts_ptr->height);

		// 填充点云数据
		if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
		{
			// patch类型：使用质心坐标
			for (size_t i = 0; i < this_pts_index.size(); i++)
			{
				this_pts_ptr->points[i].x = iter_class->second[this_pts_index[i].first]->mean_metric(0);
				this_pts_ptr->points[i].y = iter_class->second[this_pts_index[i].first]->mean_metric(1);
				this_pts_ptr->points[i].z = iter_class->second[this_pts_index[i].first]->mean_metric(2);
			}
		}
		else if (iter_class->first == PatchType::SOLID || iter_class->first == PatchType::STOP)
		{
			// line类型：使用线段点坐标
			for (size_t i = 0; i < this_pts_index.size(); i++)
			{
				this_pts_ptr->points[i].x = iter_class->second[this_pts_index[i].first]->line_points_metric[this_pts_index[i].second](0);
				this_pts_ptr->points[i].y = iter_class->second[this_pts_index[i].first]->line_points_metric[this_pts_index[i].second](1);
				this_pts_ptr->points[i].z = iter_class->second[this_pts_index[i].first]->line_points_metric[this_pts_index[i].second](2);
			}
		}

		// 构建KD树
		kdtree.setInputCloud(this_pts_ptr);
	}

	return 0;
}

/**
 * @brief 实例级地图匹配
 * 将当前帧中的道路标记实例与地图中的实例进行匹配
 * @param config 传感器配置
 * @param frame 当前帧
 * @param mode 匹配模式: 0=正常, 1=严格
 * @return 匹配结果 <类型, <地图实例索引, 帧实例索引>对列表>
 */
map<PatchType, vector<pair<int, int>>> RoadInstancePatchMap::mapMatch(const SensorConfig &config,
	RoadInstancePatchFrame& frame, int mode)
{
	bool wide_search = true;  // 是否使用宽搜索范围
	double search_radius = wide_search ? 20 : 10;  // 搜索半径(米)
	map<PatchType, vector<pair<int, int>>> match_pairs;  // 匹配结果

	// 遍历当前帧中的每种道路标记类型
	for (auto iter_class = frame.patches.begin(); iter_class != frame.patches.end(); iter_class++)
	{
		match_pairs[iter_class->first] = vector<pair<int, int>>();

		// 只处理支持的道路标记类型
		if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE
			|| iter_class->first == PatchType::SOLID || iter_class->first == PatchType::STOP)
		{
			// 遍历当前类型的每个实例
			for (int i = 0; i < iter_class->second.size(); i++)
			{
				auto& this_patch = iter_class->second[i];
				if(!this_patch->valid_add_to_map) continue;  // 跳过无效实例

				// 将实例质心转换到世界坐标系
				Vector3d mean_metric = frame.t + frame.R * this_patch->mean_metric;

				// 在地图的KD树中搜索最近邻
				vector<int> indice2;   // 搜索结果索引
				vector<float> dist2;   // 搜索结果距离
				class_kdtree[iter_class->first].radiusSearch(
					pcl::PointXYZ((float)mean_metric(0), (float)mean_metric(1), 0.0),
					search_radius, indice2, dist2);

				// 如果找到匹配候选
				if (indice2.size() > 0)
				{
					// 对patch类型进行额外的几何约束检查
					if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
					{
						// 检查高度和宽度差异是否在合理范围内
						if (fabs(this->patches[iter_class->first][class_pts_index[iter_class->first][indice2[0]].first]->h() - frame.patches[iter_class->first][i]->h()) > 2.0
							|| fabs(this->patches[iter_class->first][class_pts_index[iter_class->first][indice2[0]].first]->w() - frame.patches[iter_class->first][i]->w()) > 2.0)
							continue;

						// 计算方向向量的夹角(当前被注释掉)
						Vector3d n1 = this->patches[iter_class->first][class_pts_index[iter_class->first][indice2[0]].first]->mean_metric;
						Vector3d n2 = frame.patches[iter_class->first][i]->mean_metric;
						double cos_theta = fabs(n1.dot(n2) / (n1.norm() * n2.norm()));

						//if (cos_theta > cos(15.0 / 180 * M_PI))
						//	continue;
					}

					// 严格模式下的额外约束
					if (mode == 1) // strict
					{
						if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
						{
							Vector3d n1 = this->patches[iter_class->first][class_pts_index[iter_class->first][indice2[0]].first]->mean_metric;
							Vector3d n2 = frame.patches[iter_class->first][i]->mean_metric;
							double cos_theta = fabs(n1.dot(n2) / (n1.norm() * n2.norm()));

							// 检查距离和高度约束
							if ((this->patches[iter_class->first][class_pts_index[iter_class->first][indice2[0]].first]->mean_metric - mean_metric).norm()
									> config.localization_max_strict_match_dist
								|| this->patches[iter_class->first][class_pts_index[iter_class->first][indice2[0]].first]->h() < 1.5)
								continue;
						}
					}

					// 添加匹配对: <地图实例索引, 帧实例索引>
					match_pairs[iter_class->first].push_back(make_pair(class_pts_index[iter_class->first][indice2[0]].first, i));
				}
			}
		}
	}

	return match_pairs;
}

/**
 * @brief 线段匹配函数
 * 将帧中的线段实例与地图中的线段实例进行点对点匹配，用于构建测量约束
 * @param config 传感器配置
 * @param frame 当前帧
 * @param road_class 道路标记类型
 * @param frame_line_count 帧中线段实例索引
 * @param map_line_count 地图中线段实例索引
 * @param mode 匹配模式: 0=正常, >0=严格模式
 * @return 匹配点对列表 <帧线段点索引, 地图线段点索引>
 */
vector<pair<int, int>> RoadInstancePatchMap::getLineMatch(const SensorConfig &config, RoadInstancePatchFrame& frame,
	PatchType road_class, int frame_line_count, int map_line_count, int mode)
{
	// 获取帧中和地图中的线段实例引用
	auto& this_patch_frame = frame.patches[road_class][frame_line_count];
	auto& this_patch_map = this->patches[road_class][map_line_count];

	vector<pair<int, int>> match_pairs;  // 匹配点对结果
	Vector3d last_w_p = Vector3d(0, 0, 0);  // 上一个处理的世界坐标点

	// 遍历帧中线段的每个点
	for (int i = 0; i < this_patch_frame->line_points_metric.size(); i++)
	{
		// 将帧中的线段点转换到世界坐标系
		Vector3d w_p = frame.R * this_patch_frame->line_points_metric[i] + frame.t;

		// 对于实线类型，进行采样间隔控制以避免过密的匹配点
		if (road_class == PatchType::SOLID)
		{
			if ((w_p - last_w_p).norm() < config.localization_solid_sample_interval) continue;
		}

		// 计算当前点到地图线段所有点的距离
		map<double, int> dist;  // <距离, 地图点索引>
		for (int j = 0; j < this_patch_map->line_points_metric.size(); j++)
		{
			dist.emplace((w_p - this_patch_map->line_points_metric[j]).norm(), j);
		}

		// 获取距离最近的两个点
		auto iter_dist = dist.begin();      // 最近点
		auto iter_dist_2 = dist.begin();
		iter_dist_2++;                      // 次近点

		// 选择索引较小的点作为匹配点(确保线段方向一致性)
		int match_id = min(iter_dist->second, iter_dist_2->second);

		// 跳过线段端点附近的点(避免边界效应)
		if (match_id == 0 || match_id > this_patch_map->line_points_metric.size() - 3)
		{
			continue;
		}

		last_w_p = w_p;  // 更新上一个处理点

		// 获取地图线段上的匹配点及其相邻点
		Vector3d w_p0 = this_patch_map->line_points_metric[match_id];      // 匹配点
		Vector3d w_p1 = this_patch_map->line_points_metric[match_id + 1];  // 下一个点

		// 计算线段方向向量 (w_p1 - w_p0)
		double Dxlj = w_p1[0] - w_p0[0];
		double Dylj = w_p1[1] - w_p0[1];
		double Dzlj = w_p1[2] - w_p0[2];

		// 计算当前点到匹配点的向量 (w_p - w_p0)
		double Dxli = w_p[0] - w_p0[0];
		double Dyli = w_p[1] - w_p0[1];
		double Dzli = w_p[2] - w_p0[2];

		// 计算两个向量的叉积 (线段方向 × 点到线段的向量)
		double a = Dylj * Dzli - Dzlj * Dyli;
		double b = Dzlj * Dxli - Dxlj * Dzli;
		double c = Dxlj * Dyli - Dylj * Dxli;

		// 计算点到线段的垂直距离
		double Dvljxvli = pow((a * a + b * b + c * c), 0.5);  // 叉积的模长
		double Dvlj = pow(Dxlj * Dxlj + Dylj * Dylj + Dzlj * Dzlj, 0.5);  // 线段长度
		double ddd = Dvljxvli / Dvlj;  // 点到线段的距离

		// 严格模式下检查距离约束
		if (mode > 0 && ddd > config.localization_max_strict_match_dist) continue;

		// 添加匹配点对
		match_pairs.push_back(make_pair(i, match_id));
	}

	return match_pairs;
}

/**
 * @brief 解冻地图中的所有实例
 * 将所有实例的frozen和merged标志设为false，使其可以参与后续的合并操作
 * @return 成功返回0
 */
int RoadInstancePatchMap::unfreeze()
{
	// 遍历所有类型的道路标记实例
	for (auto iter_class = patches.begin(); iter_class != patches.end(); iter_class++)
		for (int i = 0; i < iter_class->second.size(); i++)
		{
			iter_class->second[i]->frozen = false;  // 解除冻结状态
			iter_class->second[i]->merged = false;  // 解除合并状态
		}
	return 0;
}

/**
 * @brief 合并两个地图
 * 将另一个地图的实例简单堆叠到当前地图中，不进行实例间的合并
 * @param road_map_other 要合并的另一个地图
 * @return 成功返回0
 */
int RoadInstancePatchMap::mergeMap(const RoadInstancePatchMap& road_map_other)
{
	auto refp0 = this->ref;           // 当前地图的参考点
	auto refp1 = road_map_other.ref;  // 另一个地图的参考点

	// 确保参考点在地球表面附近(6000-7000公里，即地心距离)
	assert(refp0.norm() > 6000e3 && refp0.norm() < 7000e3);
	assert(refp1.norm() > 6000e3 && refp1.norm() < 7000e3);

	// 注释掉的代码：原本用于更复杂的坐标转换
	//Eigen::Matrix3d Ren0 = calcRne(refp0.pos);
	//Eigen::Vector3d ten0 = refp0.pos;
	//Eigen::Matrix3d Ren1 = calcRne(refp1.pos);
	//Eigen::Vector3d ten1 = refp1.pos;

	// 找到当前地图中最大的实例ID，为新加入的实例分配不冲突的ID
	long long next_id = -1;
	for (auto iter_class = this->patches.begin(); iter_class != this->patches.end(); iter_class++)
	{
		for (int i = 0; i < iter_class->second.size(); i++)
		{
			if (iter_class->second[i]->id > next_id)
				next_id = iter_class->second[i]->id;
		}
	}
	next_id++;  // 新实例的起始ID

	// 计算两个地图参考点之间的旋转矩阵
	Matrix3d Rn0e = calcRne(refp0);  // 当前地图参考点的旋转矩阵
	Matrix3d Rn1e = calcRne(refp1);  // 另一个地图参考点的旋转矩阵

	// 遍历另一个地图的所有实例
	for (auto iter_class = road_map_other.patches.begin(); iter_class != road_map_other.patches.end(); iter_class++)
	{
		for (int i = 0; i < iter_class->second.size(); i++)
		{
			auto& this_patch = iter_class->second[i];
			auto patch_copy = this_patch;  // 复制实例
			patch_copy->id += next_id;     // 分配新的ID避免冲突

			// 假设两个参考点的旋转矩阵相近，不确定性不会改变
			// 坐标转换公式: P_new = Rn0e * (Rn1e^T * P_old + refp1 - refp0)

			// 转换边界框顶点坐标
			for (int ii = 0; ii < 4; ii++)
				patch_copy->b_point_metric[ii] = Rn0e * (Rn1e.transpose() * patch_copy->b_point_metric[ii] + refp1 - refp0);

			// 转换线段点坐标
			for (int ii = 0; ii < patch_copy->line_points_metric.size(); ii++)
				patch_copy->line_points_metric[ii] = Rn0e * (Rn1e.transpose() * patch_copy->line_points_metric[ii] + refp1 - refp0);

			// 转换质心坐标
			patch_copy->mean_metric = Rn0e * (Rn1e.transpose() * patch_copy->mean_metric + refp1 - refp0);

			// 将转换后的实例添加到当前地图
			this->patches[iter_class->first].push_back(patch_copy);
		}
	}
	return 0;
}

/**
 * @brief 清理地图中的无效实例
 * 移除包含NaN值、尺寸过小或其他异常的道路标记实例
 * @return 成功返回0
 */
int RoadInstancePatchMap::cleanMap()
{
	// 遍历所有类型的道路标记
	for (auto iter_class = patches.begin(); iter_class != patches.end(); iter_class++)
	{
		// 遍历当前类型的所有实例(使用迭代器以便安全删除)
		for (auto iter_patch = iter_class->second.begin(); iter_patch != iter_class->second.end();)
		{
			bool bad_flag = false;  // 标记是否为无效实例

			// 对线段类型(实线、停止线)进行检查
			if (iter_class->first == PatchType::SOLID || iter_class->first == PatchType::STOP)
			{
				// 检查线段点是否包含NaN值
				for (int i = 0; i < (*iter_patch)->line_points_metric.size(); i++)
				{
					if (isnan((*iter_patch)->line_points_metric[i].norm()))
					{
						bad_flag = true;
					}
				}

				// 检查线段是否太短或点数太少
				if ((*iter_patch)->line_points_metric.size() < 5 ||
					((*iter_patch)->line_points_metric.back() - (*iter_patch)->line_points_metric.front()).norm() < 5)
					bad_flag = true;
			}
			// 对patch类型(虚线、引导标记)进行检查
			else if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
			{
				// 检查边界框顶点和质心是否包含NaN值，以及尺寸是否过小
				if (isnan((*iter_patch)->b_point_metric[0].norm()) ||
					isnan((*iter_patch)->b_point_metric[1].norm()) ||
					isnan((*iter_patch)->b_point_metric[2].norm()) ||
					isnan((*iter_patch)->b_point_metric[3].norm()) ||
					isnan((*iter_patch)->mean_metric.norm()) ||
					(*iter_patch)->w() < 0.01 ||  // 宽度小于1cm
					(*iter_patch)->h() < 0.01)    // 高度小于1cm
				{
					bad_flag = true;
				}
			}

			// 如果是无效实例，从容器中删除
			if (bad_flag)
				iter_patch = iter_class->second.erase(iter_patch);
			else
			{
				iter_patch++;  // 继续下一个实例
			}
		}
	}

	return 0;
}

/**
 * @brief 地理配准函数
 * 基于新的轨迹信息重新计算地图中道路标记实例的位置
 * @param new_traj 新的轨迹数据
 * @param lines_vis 可视化线段容器(用于调试显示)
 * @return 成功返回0
 */
int RoadInstancePatchMap::geoRegister(const Trajectory& new_traj, vector<VisualizedInstance>& lines_vis)
{
	std::cerr<<"[INFO] Start geo-registering."<<std::endl;

	// 设置可视化模板(用于调试显示连接线)
	VisualizedInstance vis_instance_template;
	vis_instance_template.type = VisualizedPatchType::LINE_SEGMENT;
	vis_instance_template.pts = vector<Vector3d>(2);
	vis_instance_template.pts_color = vector<Vector3d>(2, Eigen::Vector3d(0, 0, 1.0));  // 蓝色
	vis_instance_template.alpha = 0.25f;      // 透明度
	vis_instance_template.linewidth = 0.5f;   // 线宽

	// 更新地图参考点
	ref = new_traj.ref;

	// 保存旧的位姿信息
	map<long long, pair<Matrix3d, Vector3d>> old_poses = queued_poses;
	map<long long, pair<Matrix3d, Vector3d>> new_poses;

	// 根据时间戳匹配新旧轨迹中的位姿
	for (auto iter = timestamps.begin(); iter != timestamps.end(); iter++)
	{
		// 在新轨迹中查找对应时间戳的位姿(允许1ms误差)
		auto iiter = new_traj.poses.lower_bound(iter->second - 0.001);
		if (fabs(iiter->first - iter->second) < 0.01)  // 时间匹配精度10ms
			new_poses[iter->first] = make_pair(iiter->second.R, iiter->second.t);
	}

	// 遍历所有类型的道路标记实例
	for (auto iter_class = patches.begin(); iter_class != patches.end(); iter_class++)
	{
		auto start = chrono::system_clock::now();  // 计时开始

		for (int i = 0; i < iter_class->second.size(); i++)
		{
			auto& this_patch = iter_class->second[i];
			if (!(this_patch->frozen)) continue; // TODO: 只处理冻结的实例

			// 处理线段类型的道路标记(实线、停止线)
			if (iter_class->first == PatchType::SOLID || iter_class->first == PatchType::STOP)
			{
				// 确保关联帧数量与线段点数量一致
				if (this_patch->linked_frames.size() != this_patch->line_points_metric.size()) continue;

				// 获取所有线段点的指针
				vector<Eigen::Vector3d*> pts;
				for (int j = 0; j < this_patch->line_points_metric.size(); j++)
					pts.push_back(&this_patch->line_points_metric[j]);

				// 对每个线段点进行地理配准
				for (int j = 0; j < pts.size(); j++)
				{
					Vector3d twf_count; twf_count.setZero();  // 累计新位置
					int ccount = 0;  // 计数器

					// 遍历该点关联的所有帧
					for (int iii = 0; iii < this_patch->linked_frames[j].size(); iii++)
					{
						long long id = this_patch->linked_frames[j][iii];

						// 坐标转换：世界坐标 -> 车体坐标 -> 新世界坐标
						Eigen::Vector3d tvf = old_poses[id].first.transpose() * (*pts[j] - old_poses[id].second);  // 转到旧车体坐标
						Eigen::Vector3d twf = new_poses[id].first * tvf + new_poses[id].second;  // 转到新世界坐标
						twf_count += twf;
						ccount += 1;

						// 添加可视化连接线(从点到旧位姿)
						vis_instance_template.pts[0] = *pts[j];
						vis_instance_template.pts[1] = old_poses[id].second;
						lines_vis.push_back(vis_instance_template);
					}

					// 更新点的位置为所有关联帧计算结果的平均值
					*pts[j] = twf_count / ccount;
				}
			}
			// 处理patch类型的道路标记(虚线、引导标记)
			else
			{
				if (this_patch->linked_frames.size() == 0) continue;
				vector<Eigen::Vector3d*> pts;  // 待处理的点(质心和边界框顶点)

				Vector3d twf_count;  // 累计新位置
				int ccount;          // 计数器

				twf_count.setZero();
				ccount = 0;

				// 处理质心的地理配准
				for (int iii = 0; iii < this_patch->linked_frames[0].size(); iii++)
				{
					long long id = this_patch->linked_frames[0][iii];

					// 坐标转换：世界坐标 -> 旧车体坐标 -> 新世界坐标
					Eigen::Vector3d tvf = old_poses[id].first.transpose() * (this_patch->mean_metric - old_poses[id].second);
					Eigen::Vector3d twf = new_poses[id].first * tvf + new_poses[id].second;
					twf_count += twf;
					ccount += 1;

					// 添加可视化连接线(从质心到旧位姿)
					vis_instance_template.pts[0] = this_patch->mean_metric;
					vis_instance_template.pts[1] = old_poses[id].second;
					lines_vis.push_back(vis_instance_template);
				}

				// 更新质心位置
				this_patch->mean_metric = twf_count / ccount;

				// 处理边界框四个顶点的地理配准
				for (int j = 0; j < 4; j++)
				{
					twf_count.setZero();
					ccount = 0;

					// 遍历关联的所有帧
					for (int iii = 0; iii < this_patch->linked_frames[0].size(); iii++)
					{
						long long id = this_patch->linked_frames[0][iii];

						// 坐标转换：世界坐标 -> 旧车体坐标 -> 新世界坐标
						Eigen::Vector3d tvf = old_poses[id].first.transpose() * (this_patch->b_point_metric[j] - old_poses[id].second);
						Eigen::Vector3d twf = new_poses[id].first * tvf + new_poses[id].second;
						//if (iter_class->first == PatchType::DASHED) std::cerr <<j<<" "<< new_poses[id].second.transpose() << std::endl;
						twf_count += twf;
						ccount += 1;

						// 添加可视化连接线(从边界框顶点到旧位姿)
						vis_instance_template.pts[0] = this_patch->b_point_metric[j];
						vis_instance_template.pts[1] = old_poses[id].second;
						lines_vis.push_back(vis_instance_template);
					}

					// 更新边界框顶点位置
					this_patch->b_point_metric[j] = twf_count / ccount;
				}
			}
		}
	}
	return 0;
}


/**
 * @brief 合并地图中的道路标记实例
 * 这是RoadLib的核心函数，负责将相似的道路标记实例聚类合并
 * @param config 传感器配置参数
 * @param mode 合并模式: 0=增量建图, 1=地图合并/检查
 * @param Rwv 车辆到世界坐标系旋转矩阵(mode 0时使用)
 * @param twv 车辆到世界坐标系平移向量(mode 0时使用)
 * @return 成功返回0
 */
int RoadInstancePatchMap::mergePatches(const SensorConfig& config, const int mode, const Eigen::Matrix3d& Rwv, const Eigen::Vector3d& twv)
{
	map<PatchType, vector<shared_ptr<RoadInstancePatch>>> patches_new;  // 合并后的新实例容器

	// 清理冗余的道路标记实例
	if (ignore_frame_ids.size() > 0)
		for (auto iter_class = patches.begin(); iter_class != patches.end(); iter_class++)
		{
			for (auto iter_patch = iter_class->second.begin(); iter_patch != iter_class->second.end();)
			{
				// 检查当前实例是否来自需要忽略的帧
				if (ignore_frame_ids.find((*iter_patch)->frame_id) != ignore_frame_ids.end())
				{
					// 对实线类型：保留所有实例(注释掉删除操作)
					if (iter_class->first == PatchType::SOLID)
						iter_patch++;
					//iter_patch = iter_class->second.erase(iter_patch);

					// 对其他类型：根据感知距离决定是否删除
					else if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE || iter_class->first == PatchType::STOP)
					{
						// 如果感知距离超过阈值+0.5米，则删除该实例
						if ((*iter_patch)->percept_distance > ignore_frame_ids[(*iter_patch)->frame_id] + 0.5)
						{
							iter_patch = iter_class->second.erase(iter_patch);
						}
						else
						{
							std::cout << "[INFO] Catch a safe patch!" << std::endl;
							iter_patch++;
						}
					}
				}
				else
				{
					iter_patch++;
				}
			}
		}
	ignore_frame_ids.clear();

	// start patch clustering and merging
	for (auto iter_class = patches.begin(); iter_class != patches.end(); iter_class++)
	{
		auto start = chrono::system_clock::now();
		vector<cv::Point2f> pts;
		vector<int> cluster_flag(iter_class->second.size(), -1);
		map<int, vector<int>> cluster_index;
		int cluster_id = 0;

		for (int i = 0; i < iter_class->second.size(); i++)
		{
			auto& iter_instance = iter_class->second[i];
			pts.push_back(cv::Point2f((iter_instance)->mean_metric(0),
				(iter_instance)->mean_metric(1)));
		}
		cv::Mat pts_vec = cv::Mat(pts).reshape(1);
		pts_vec.convertTo(pts_vec, CV_32F);
		cv::flann::KDTreeIndexParams indexParams(2);
		cv::flann::Index kdtree(pts_vec, indexParams);
		long long comp_count = 0;

		if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
		{
			for (int i = 0; i < iter_class->second.size(); i++)
			{
				if (!iter_class->second[i]->valid_add_to_map) continue;
				if (cluster_flag[i] == -1)
				{
					vector<int> indice(1000);
					vector<float> dist(1000);
					//int num = kdtree.radiusSearch(vector<float>({ pts[i].x,pts[i].y }), indice, dist, 20.0, 1000);
					int num = pts.size() > 500 ? 500 : pts.size();
					kdtree.knnSearch(vector<float>({ pts[i].x,pts[i].y }), indice, dist, num);

					vector<int> intersection_flag(num, -1);
					queue<int> new_intersection_instance; new_intersection_instance.push(0);
					vector<int> same_cluster_flag; // for bi-directional mathching

					if (iter_class->second[i]->frozen || iter_class->second[i]->merged)
					{
						intersection_flag[0] = 1;
					}
					else
					{
						while (new_intersection_instance.size() > 0)
						{
							int cur_id = new_intersection_instance.front();
							new_intersection_instance.pop();
							for (int j = 0; j < num; j++)
							{
								cv::Mat rect_intersection;

								if (intersection_flag[j] >= 1) continue;
								if (iter_class->second[indice[cur_id]]->frozen || iter_class->second[indice[j]]->frozen)
								{
									intersection_flag[j] = 0;
								}
								else
								{
									intersection_flag[j] = cv::rotatedRectangleIntersection(
										cv::RotatedRect(
											cv::Point2f(iter_class->second[indice[cur_id]]->mean_metric(0), iter_class->second[indice[cur_id]]->mean_metric(1)),
											cv::Size2f(iter_class->second[indice[cur_id]]->h(), iter_class->second[indice[cur_id]]->w()),
											atan2(iter_class->second[indice[cur_id]]->d()(1), iter_class->second[indice[cur_id]]->d()(0)) * R2D),
										cv::RotatedRect(
											cv::Point2f(iter_class->second[indice[j]]->mean_metric(0), iter_class->second[indice[j]]->mean_metric(1)),
											cv::Size2f(iter_class->second[indice[j]]->h(), iter_class->second[indice[j]]->w()),
											atan2(iter_class->second[indice[j]]->d()(1), iter_class->second[indice[j]]->d()(0)) * R2D)
										, rect_intersection);
								}

								if (intersection_flag[j] >= 1)
								{
									if (cluster_flag[indice[j]] != -1) // Bidirectinal matching!!!!
									{
										same_cluster_flag.push_back(cluster_flag[indice[j]]);
										continue;
									}
									new_intersection_instance.push(j);
								}

							}
						}
					}

					if (num >= 1)
					{
						for (int j = 0; j < num; j++)
						{
							if (intersection_flag[j] > 0)
								cluster_flag[indice[j]] = cluster_id;
						}
						cluster_id++;
					}
				}
			}

			for (int i = 0; i < iter_class->second.size(); i++)
			{
				if (cluster_flag[i] != -1)
				{
					(cluster_index[cluster_flag[i]]).push_back(i);
				}
			}

			for (int i = 0; i < cluster_id; i++)
			{
				if (cluster_index[i].size() == 0) continue; // This shouldn't happen. To be fixed.

				Eigen::Vector3d mean_ref = iter_class->second[cluster_index[i][0]]->mean_metric;

				// still active
				if ((mode == 0 && (Rwv.transpose() * (mean_ref - twv)).y() > -config.mapping_patch_freeze_distance && twv != Vector3d::Zero())
					|| (twv == Vector3d::Zero() && cluster_index[i].size() == 1 && iter_class->second[cluster_index[i][0]]->frozen == false))
				{
					for (int j = 0; j < cluster_index[i].size(); j++)
					{
						patches_new[iter_class->first].push_back(iter_class->second[cluster_index[i][j]]);
					}
					continue;
				}
				if (mode == 0 && cluster_index[i].size() == 1 && iter_class->second[cluster_index[i][0]]->frozen == false 
					&& iter_class->second[cluster_index[i][0]]->merged == false)
					continue;


				vector<double> eigen_value_list;
				for (int j = 0; j < cluster_index[i].size(); j++)
				{
					eigen_value_list.push_back(iter_class->second[cluster_index[i][j]]->h());
				}
				std::sort(eigen_value_list.begin(), eigen_value_list.end());
				//if (eigen_value_list.size() < 4) continue;
				double median_eigen_value = eigen_value_list[(eigen_value_list.size()) / 2];



				vector<Eigen::Vector4d> ds;
				vector<Eigen::Vector4d> uncs;
				vector<double> angles;

				for (int j = 0; j < cluster_index[i].size(); j++)
				{
					auto& patch = iter_class->second[cluster_index[i][j]];
					Vector3d ud0 = (mean_ref - patch->b_point_metric[0]).cross((patch->b_point_metric[1] - patch->b_point_metric[0]).normalized()); float d0 = ud0.norm(); if (ud0(2) > 0) d0 *= -1;
					Vector3d ud1 = (mean_ref - patch->b_point_metric[1]).cross((patch->b_point_metric[2] - patch->b_point_metric[1]).normalized()); float d1 = ud1.norm(); if (ud1(2) > 0) d1 *= -1;
					Vector3d ud2 = (mean_ref - patch->b_point_metric[2]).cross((patch->b_point_metric[3] - patch->b_point_metric[2]).normalized()); float d2 = ud2.norm(); if (ud2(2) > 0) d2 *= -1;
					Vector3d ud3 = (mean_ref - patch->b_point_metric[3]).cross((patch->b_point_metric[0] - patch->b_point_metric[3]).normalized()); float d3 = ud3.norm(); if (ud3(2) > 0) d3 *= -1;
					ds.push_back(Eigen::Vector4d(d0, d1, d2, d3));
					uncs.push_back(Eigen::Vector4d(patch->b_unc_dist[0], patch->b_unc_dist[1], patch->b_unc_dist[2], patch->b_unc_dist[3]));
					Vector3d uu = patch->b_point_metric[2] - patch->b_point_metric[1];
					angles.push_back(atan2(uu.y(), uu.x()));
				}

				Eigen::Vector4d ds_new;
				Eigen::Vector4d uncs_new;
				double angle_ref = angles[0];
				for (int ii = angles.size() - 1; ii >= 0; ii--)
				{
					angles[ii] = fmod(angles[ii] - angle_ref + M_PI * 3, M_PI * 2) - M_PI;
				}
				auto angles_temp = angles;
				std::sort(angles_temp.begin(), angles_temp.end());
				double angle_median = angle_ref + angles_temp[angles_temp.size() / 2];

				// Optimize the bounding box.
				for (int dd = 0; dd < 4; dd++)
				{
					double x = -99999;
					for (int ii = 0; ii < ds.size(); ii++)
					{
						if (x < ds[ii](dd)) x = ds[ii](dd);
					}

					double min_unc;
					double min_x = 0.0;
					for (int iiter = 0; iiter < 10; iiter++)
					{
						min_unc = 1000;
						double H = 0.0;
						double v = 0.0;
						for (int ii = 0; ii < ds.size(); ii++)
						{
							if (mode == 0 && fabs(angles[ii] + angle_ref - angle_median) > 3.0 / 180 * M_PI)
							{
								double zzz = ds_new.norm();
								continue;
							}
							if (uncs[ii](dd) > 1.0) continue;
							double l = (ds[ii](dd) - x) / uncs[ii](dd);
							double A = 1 / uncs[ii](dd);
							double downweight = sqrt(fabs(l));
							if (downweight > 1.5) continue;
							l /= 1;
							A /= 1;
							H += A * A;
							v += A * l;
							if (uncs[ii](dd) < min_unc)
							{
								min_x = ds[ii](dd);
								min_unc = uncs[ii](dd);
							}
						}
						x += v / H;
					}
					ds_new(dd) = min_x;
					uncs_new(dd) = min_unc;
				}
				Matrix3d direction_rotation;
				angle_median = -angle_median + M_PI / 2;
				direction_rotation << cos(angle_median), sin(angle_median), 0,
					-sin(angle_median), cos(angle_median), 0,
					0, 0, 1;
#ifdef DEBUG
				Eigen::Vector3d px0(-ds_new(3), -ds_new(0), 0);
				Eigen::Vector3d px1(ds_new(1), -ds_new(0), 0);
				Eigen::Vector3d px2(ds_new(1), ds_new(2), 0);
				Eigen::Vector3d px3(-ds_new(3), ds_new(2), 0);

				Vector3d b_point_metric[4];
				b_point_metric[0] = mean_ref + direction_rotation * px0;
				b_point_metric[1] = mean_ref + direction_rotation * px1;
				b_point_metric[2] = mean_ref + direction_rotation * px2;
				b_point_metric[3] = mean_ref + direction_rotation * px3;
				//patch_new.b_unc_dist[0] = uncs_new(0);
				//patch_new.b_unc_dist[1] = uncs_new(1);
				//patch_new.b_unc_dist[2] = uncs_new(2);
				//patch_new.b_unc_dist[3] = uncs_new(3);


				for (int j = 0; j < cluster_index[i].size(); j++)
				{
					auto& patch = iter_class->second[cluster_index[i][j]];
					Vector3d ud0 = (mean_ref - patch->b_point_metric[0]).cross((patch->b_point_metric[1] - patch->b_point_metric[0]).normalized()); float d0 = ud0.norm(); if (ud0(2) > 0) d0 *= -1;
					Vector3d ud1 = (mean_ref - patch->b_point_metric[1]).cross((patch->b_point_metric[2] - patch->b_point_metric[1]).normalized()); float d1 = ud1.norm(); if (ud1(2) > 0) d1 *= -1;
					Vector3d ud2 = (mean_ref - patch->b_point_metric[2]).cross((patch->b_point_metric[3] - patch->b_point_metric[2]).normalized()); float d2 = ud2.norm(); if (ud2(2) > 0) d2 *= -1;
					Vector3d ud3 = (mean_ref - patch->b_point_metric[3]).cross((patch->b_point_metric[0] - patch->b_point_metric[3]).normalized()); float d3 = ud3.norm(); if (ud3(2) > 0) d3 *= -1;

					float x0 = (patch->b_point_metric[0] - mean_ref).x() * 110 + 500;
					float y0 = (patch->b_point_metric[0] - mean_ref).y() * 110 + 500;
					float x1 = (patch->b_point_metric[1] - mean_ref).x() * 110 + 500;
					float y1 = (patch->b_point_metric[1] - mean_ref).y() * 110 + 500;
					float x2 = (patch->b_point_metric[2] - mean_ref).x() * 110 + 500;
					float y2 = (patch->b_point_metric[2] - mean_ref).y() * 110 + 500;
					float x3 = (patch->b_point_metric[3] - mean_ref).x() * 110 + 500;
					float y3 = (patch->b_point_metric[3] - mean_ref).y() * 110 + 500;
					cv::line(mm, cv::Point2f(x0, 1000 - y0), cv::Point2f(x1, 1000 - y1), cv::Scalar(255, 255, 255));
					cv::line(mm, cv::Point2f(x1, 1000 - y1), cv::Point2f(x2, 1000 - y2), cv::Scalar(255, 255, 255));
					cv::line(mm, cv::Point2f(x2, 1000 - y2), cv::Point2f(x3, 1000 - y3), cv::Scalar(255, 255, 255));
					//cv::line(mm, cv::Point2f(x3, 1000-y3), cv::Point2f(x0, 1000-y0), cv::Scalar(255, 255, 255));

					cv::Point2f p0 = cv::Point2f((x0 + x1) / 2, (y0 + y1) / 2);
					cv::Point2f p1 = cv::Point2f((x1 + x2) / 2, (y1 + y2) / 2);
					cv::Point2f p2 = cv::Point2f((x2 + x3) / 2, (y2 + y3) / 2);
					cv::Point2f p3 = cv::Point2f((x3 + x0) / 2, (y3 + y0) / 2);
					cv::circle(mm, cv::Point2f(p0.x, 1000 - p0.y), 5, cv::Scalar(255, 255, 255));
					cv::circle(mm, cv::Point2f(p1.x, 1000 - p1.y), 5, cv::Scalar(255, 255, 255));
					cv::circle(mm, cv::Point2f(p2.x, 1000 - p2.y), 5, cv::Scalar(255, 255, 255));
					cv::circle(mm, cv::Point2f(p3.x, 1000 - p3.y), 5, cv::Scalar(255, 255, 255));
					Eigen::Vector3d u0 = (patch->b_point_metric[1] - patch->b_point_metric[0]).normalized();
					Eigen::Vector3d u1 = (patch->b_point_metric[2] - patch->b_point_metric[1]).normalized();

					cv::Point2f pp0, pp1;
					pp0 = p0 - cv::Point2f(u1.x(), u1.y()) * patch->b_unc_dist[0] * 110; pp1 = p0 + cv::Point2f(u1.x(), u1.y()) * patch->b_unc_dist[0] * 110;
					cv::line(mm, cv::Point2f(pp0.x, 1000 - pp0.y), cv::Point2f(pp1.x, 1000 - pp1.y), cv::Scalar(0, 0, 255));
					pp0 = p1 - cv::Point2f(u0.x(), u0.y()) * patch->b_unc_dist[1] * 110; pp1 = p1 + cv::Point2f(u0.x(), u0.y()) * patch->b_unc_dist[1] * 110;
					cv::line(mm, cv::Point2f(pp0.x, 1000 - pp0.y), cv::Point2f(pp1.x, 1000 - pp1.y), cv::Scalar(0, 0, 255));
					pp0 = p2 - cv::Point2f(u1.x(), u1.y()) * patch->b_unc_dist[2] * 110; pp1 = p2 + cv::Point2f(u1.x(), u1.y()) * patch->b_unc_dist[2] * 110;
					cv::line(mm, cv::Point2f(pp0.x, 1000 - pp0.y), cv::Point2f(pp1.x, 1000 - pp1.y), cv::Scalar(0, 0, 255));
					pp0 = p3 - cv::Point2f(u0.x(), u0.y()) * patch->b_unc_dist[3] * 110; pp1 = p3 + cv::Point2f(u0.x(), u0.y()) * patch->b_unc_dist[3] * 110;
					cv::line(mm, cv::Point2f(pp0.x, 1000 - pp0.y), cv::Point2f(pp1.x, 1000 - pp1.y), cv::Scalar(0, 0, 255));
					std::cout << patch->b_unc_dist[0] << " "
						<< patch->b_unc_dist[1] << " "
						<< patch->b_unc_dist[2] << " "
						<< patch->b_unc_dist[3] << std::endl;
				}



				float x0 = (b_point_metric[0] - mean_ref).x() * 110 + 500;
				float y0 = (b_point_metric[0] - mean_ref).y() * 110 + 500;
				float x1 = (b_point_metric[1] - mean_ref).x() * 110 + 500;
				float y1 = (b_point_metric[1] - mean_ref).y() * 110 + 500;
				float x2 = (b_point_metric[2] - mean_ref).x() * 110 + 500;
				float y2 = (b_point_metric[2] - mean_ref).y() * 110 + 500;
				float x3 = (b_point_metric[3] - mean_ref).x() * 110 + 500;
				float y3 = (b_point_metric[3] - mean_ref).y() * 110 + 500;
				cv::line(mm, cv::Point2f(x0, 1000 - y0), cv::Point2f(x1, 1000 - y1), cv::Scalar(0, 255, 0));
				cv::line(mm, cv::Point2f(x1, 1000 - y1), cv::Point2f(x2, 1000 - y2), cv::Scalar(0, 255, 0));
				cv::line(mm, cv::Point2f(x2, 1000 - y2), cv::Point2f(x3, 1000 - y3), cv::Scalar(0, 255, 0));
				//cv::line(mm, cv::Point2f(x3, 1000-y3), cv::Point2f(x0, 1000-y0), cv::Scalar(255, 255, 255));

				cv::Point2f p0 = cv::Point2f((x0 + x1) / 2, (y0 + y1) / 2);
				cv::Point2f p1 = cv::Point2f((x1 + x2) / 2, (y1 + y2) / 2);
				cv::Point2f p2 = cv::Point2f((x2 + x3) / 2, (y2 + y3) / 2);
				cv::Point2f p3 = cv::Point2f((x3 + x0) / 2, (y3 + y0) / 2);
				cv::circle(mm, cv::Point2f(p0.x, 1000 - p0.y), 5, cv::Scalar(0, 255, 0));
				cv::circle(mm, cv::Point2f(p1.x, 1000 - p1.y), 5, cv::Scalar(0, 255, 0));
				cv::circle(mm, cv::Point2f(p2.x, 1000 - p2.y), 5, cv::Scalar(0, 255, 0));
				cv::circle(mm, cv::Point2f(p3.x, 1000 - p3.y), 5, cv::Scalar(0, 255, 0));
				cv::circle(mm, cv::Point2f(500, 500), 15, cv::Scalar(255, 255, 0));
				Eigen::Vector3d u0 = (b_point_metric[1] - b_point_metric[0]).normalized();
				Eigen::Vector3d u1 = (b_point_metric[2] - b_point_metric[1]).normalized();

				cv::imshow("mm_temp", mm);
				cv::waitKey(1);
#endif

				RoadInstancePatch patch_new;
				double best_score = 10000;
				int best_score_id = -1;

				for (int j = 0; j < cluster_index[i].size(); j++)
				{
					if (iter_class->second[cluster_index[i][j]]->percept_distance < best_score
						&& iter_class->second[cluster_index[i][j]]->h() >= median_eigen_value - 0.1)
					{
						best_score = iter_class->second[cluster_index[i][j]]->percept_distance;
						best_score_id = j;
					}
				}
				if (((ds_new(3) + ds_new(1)) * (ds_new(2) + ds_new(0)) < 2.0 || (ds_new(2) + ds_new(0)) < 0.5) && iter_class->first == PatchType::GUIDE)
					continue;

				patch_new = *iter_class->second[cluster_index[i][best_score_id]];
				Eigen::Vector3d p0(-ds_new(3), -ds_new(0), 0);
				Eigen::Vector3d p1(ds_new(1), -ds_new(0), 0);
				Eigen::Vector3d p2(ds_new(1), ds_new(2), 0);
				Eigen::Vector3d p3(-ds_new(3), ds_new(2), 0);
				patch_new.b_point_metric[0] = mean_ref + direction_rotation * p0;
				patch_new.b_point_metric[1] = mean_ref + direction_rotation * p1;
				patch_new.b_point_metric[2] = mean_ref + direction_rotation * p2;
				patch_new.b_point_metric[3] = mean_ref + direction_rotation * p3;
				patch_new.b_unc_dist[0] = uncs_new(0);
				patch_new.b_unc_dist[1] = uncs_new(1);
				patch_new.b_unc_dist[2] = uncs_new(2);
				patch_new.b_unc_dist[3] = uncs_new(3);
				patch_new.merged = true;
				// patch_new.frozen = true;


				patches_new[iter_class->first].push_back(make_shared<RoadInstancePatch>(patch_new));
			}
		}
		else if (iter_class->first == PatchType::SOLID || iter_class->first == PatchType::STOP)
		{
			auto opt_t0 = chrono::system_clock::now();

			for (int i = 0; i < iter_class->second.size(); i++)
			{
				if (cluster_flag[i] == -1)
				{
					vector<int> indice(1000);
					vector<float> dist(1000);
					int num = pts.size() > 10 ? 10 : pts.size();
					kdtree.knnSearch(vector<float>({ pts[i].x,pts[i].y }), indice, dist, num);

					vector<int> intersection_flag(num, -1);
					queue<int> new_intersection_instance; new_intersection_instance.push(0);
					//vector<pair<Matrix3d, Vector3d>> pts_vis;

					vector<int> same_cluster_flag; // for bi-directional mathching

					if (iter_class->second[i]->frozen)
					{
						intersection_flag[0] = 1;
					}
					else
					{
						// Clustering the line segments.
						while (new_intersection_instance.size() > 0)
						{
							int cur_id = new_intersection_instance.front();
							new_intersection_instance.pop();
							for (int j = 0; j < num; j++)
							{
								cv::Mat rect_intersection;

								if (intersection_flag[j] >= 1) continue;

								auto d1 = iter_class->second[indice[cur_id]]->d();
								auto d2 = iter_class->second[indice[j]]->d();
								double costheta = d1.dot(d2);


								if (fabs(costheta) < cos(90.0 / 180 * M_PI))
								{
									intersection_flag[j] = 0;
								}
								else if ((iter_class->second[indice[cur_id]]->frozen || iter_class->second[indice[j]]->frozen) && (indice[cur_id] != indice[j]))
								{
									intersection_flag[j] = 0;
								}
								else
								{
									double min_dist = 1e9;
									double min_dist_across = 1e9;
									double min_dist_across2 = 1e9;
									double min_costheta = 1e9;
									double this_dist, this_dist_across, this_dist_across2, this_costheta;
									for (int m = 0; m < iter_class->second[indice[cur_id]]->line_points_metric.size(); m++)
										for (int n = 0; n < iter_class->second[indice[j]]->line_points_metric.size(); n++)
										{
											Vector3d direction_m_local;
											if (m == 0)  direction_m_local = (iter_class->second[indice[cur_id]]->line_points_metric[m + 1] - iter_class->second[indice[cur_id]]->line_points_metric[m]).normalized();
											else         direction_m_local = (iter_class->second[indice[cur_id]]->line_points_metric[m] - iter_class->second[indice[cur_id]]->line_points_metric[m - 1]).normalized();
											Vector3d direction_n_local;
											if (n == 0)  direction_n_local = (iter_class->second[indice[j]]->line_points_metric[n + 1] - iter_class->second[indice[j]]->line_points_metric[n]).normalized();
											else         direction_n_local = (iter_class->second[indice[j]]->line_points_metric[n] - iter_class->second[indice[j]]->line_points_metric[n - 1]).normalized();

											this_dist = (iter_class->second[indice[cur_id]]->line_points_metric[m] - iter_class->second[indice[j]]->line_points_metric[n]).norm();
											this_dist_across = fabs((iter_class->second[indice[cur_id]]->line_points_metric[m] - iter_class->second[indice[j]]->line_points_metric[n]).dot(
												Vector3d(-direction_m_local(1), direction_m_local(0), direction_m_local(2))));
											this_dist_across2 = fabs((iter_class->second[indice[cur_id]]->line_points_metric[m] - iter_class->second[indice[j]]->line_points_metric[n]).dot(
												Vector3d(-direction_n_local(1), direction_n_local(0), direction_n_local(2))));
											this_costheta = fabs(direction_m_local.dot(direction_n_local));
											if (this_dist < min_dist)
											{
												min_dist = this_dist;
												min_dist_across = this_dist_across;
												min_dist_across2 = this_dist_across2;
												min_costheta = this_costheta;
											}
											comp_count++;
										}
									if (((min_dist_across < config.mapping_line_cluster_max_across_dist2 && min_dist_across2 < config.mapping_line_cluster_max_across_dist1) ||
										(min_dist_across < config.mapping_line_cluster_max_across_dist1 && min_dist_across2 < config.mapping_line_cluster_max_across_dist2))
										&& min_dist < config.mapping_line_cluster_max_dist && min_costheta > cos(config.mapping_line_cluster_max_theta / 180 * M_PI)) intersection_flag[j] = 1;

								}
								if (intersection_flag[j] >= 1)
								{
									if (cluster_flag[indice[j]] != -1) // Bidirectinal matching!!!!
									{
										same_cluster_flag.push_back(cluster_flag[indice[j]]);
										continue;
									}
									new_intersection_instance.push(j);
								}
							}
						}
					}

					if (num >= 1)
					{
						for (int j = 0; j < num; j++)
						{
							if (intersection_flag[j] > 0)
							{
								cluster_flag[indice[j]] = cluster_id;
							}
						}

						if (same_cluster_flag.size() > 0)
						{
							sort(same_cluster_flag.begin(), same_cluster_flag.end());
							for (int j = 0; j < num; j++)
								if (intersection_flag[j] > 0)
									cluster_flag[indice[j]] = same_cluster_flag[0];

							for (int iii = 0; iii < iter_class->second.size(); iii++)
								for (int iiii = 0; iiii < same_cluster_flag.size(); iiii++)
									if (cluster_flag[iii] == same_cluster_flag[iiii]) cluster_flag[iii] = same_cluster_flag[0];
						}
						cluster_id++;
					}
				}
			}
			auto opt_t1 = chrono::system_clock::now();
			for (int i = 0; i < iter_class->second.size(); i++)
			{
				if (cluster_flag[i] != -1)
				{
					(cluster_index[cluster_flag[i]]).push_back(i);
				}
			}
			auto end1 = chrono::system_clock::now();

			for (int i = 0; i < cluster_id; i++)
			{
				vector<shared_ptr<RoadInstancePatch>> all_patches_this_cluster;
				for (int j = 0; j < cluster_index[i].size(); j++)
				{
					all_patches_this_cluster.push_back(iter_class->second[cluster_index[i][j]]);
				}
				if (all_patches_this_cluster.size() < 1) continue;
				shared_ptr<RoadInstancePatch> line_est;

				auto ret = LineCluster2SingleLine(iter_class->first, all_patches_this_cluster, line_est, Rwv);

				if (ret < 0) continue;


				patches_new[iter_class->first].push_back(line_est);


			}
		}
		auto end = chrono::system_clock::now();
	}
	patches = patches_new;

	if (mode == 0)
	{
		//** Refreshing linked frames
		// This is used for geo-registering.
		for (auto iter_class = patches.begin(); iter_class != patches.end(); iter_class++)
		{
			for (int i = 0; i < iter_class->second.size(); i++)
			{
				auto& patch = iter_class->second[i];

				if (patch->frozen) continue;

				if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
				{
					patch->linked_frames.clear();
					patch->linked_frames.resize(1);
					for (auto iter_pose = queued_poses.lower_bound(queued_poses.rbegin()->first - 200); iter_pose != queued_poses.end(); iter_pose++)
					{
						Eigen::Vector3d tvf = iter_pose->second.first.transpose() *
							(patch->mean_metric - iter_pose->second.second);
						if (tvf.x() < 10 && tvf.x() > -10 && tvf.y() > 2 && tvf.y() < 20) // in the region
						{
							patch->linked_frames[0].push_back(iter_pose->first);
						}
					}
				}
				else
				{
					patch->linked_frames.clear();
					for (int jjj = 0; jjj < patch->line_points_metric.size(); jjj++)
					{
						patch->linked_frames.push_back(vector<long long>());
						for (auto iter_pose = queued_poses.lower_bound(queued_poses.rbegin()->first - 200); iter_pose != queued_poses.end(); iter_pose++)
						{
							Eigen::Vector3d tvf = iter_pose->second.first.transpose() *
								(patch->line_points_metric[jjj] - iter_pose->second.second);
							if (tvf.x() < 10 && tvf.x() > -10 && tvf.y() > 2 && tvf.y() < 20) // in the region
							{
								patch->linked_frames[jjj].push_back(iter_pose->first);
							}
						}
					}

				}
			}
		}

		// Attention!!!
		// We simply freeze old lines to avoid troublesome cases.
		// This could be optimized.
		for (auto iter_class = patches.begin(); iter_class != patches.end(); iter_class++)
		{
			for (int i = 0; i < iter_class->second.size(); i++)
			{
				auto& patch = iter_class->second[i];
				if (iter_class->first == PatchType::SOLID || iter_class->first == PatchType::STOP)
					if ((Rwv.transpose() * (patch->line_points_metric.back() - twv)).y() < -config.mapping_line_freeze_distance ||
						patch->line_points_metric.size() > config.mapping_line_freeze_max_length || twv == Vector3d::Zero())
						patch->frozen = true;
				if (iter_class->first == PatchType::DASHED || iter_class->first == PatchType::GUIDE)
					if ((Rwv.transpose() * (patch->mean_metric - twv)).y() < -config.mapping_line_freeze_distance)
						patch->frozen = true;
			}
		}
	}
	return 0;
}
