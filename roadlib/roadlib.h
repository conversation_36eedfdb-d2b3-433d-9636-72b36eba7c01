/*******************************************************
 * Copyright (C) 2024, GREAT Group, Wuhan University
 *
 * This file is part of RoadLib.
 *
 * Licensed under the GNU General Public License v3.0;
 * you may not use this file except in compliance with the License.
 *
 * Author: <PERSON><PERSON><PERSON> (<EMAIL>)
 *******************************************************/
#pragma once

// Eigen库 - 用于线性代数运算
#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/SVD>
#include<Eigen/StdVector>

// OpenCV库 - 用于图像处理和计算机视觉
#include <opencv2/opencv.hpp>
#include <opencv2/core/eigen.hpp>
#include <opencv2/flann.hpp>

// 标准C++库
#include <iostream>
#include <filesystem>
#include <chrono>
#include <unordered_map>
#include <iomanip>
#include <set>
#include <random>

// RoadLib自定义头文件
#include "gv_utils.h"        // 几何视觉工具
#include "ipm_processer.h"   // IPM(逆透视变换)处理器
#include "utils.hpp"         // 工具函数
#include "gviewer.h"         // 3D可视化器

using namespace Eigen;
using namespace std;

/**
 * @brief 传感器配置结构体
 * 包含相机参数、道路标记检测参数、建图参数、定位参数等所有配置信息
 */
struct SensorConfig
{
public:
	SensorConfig(string path);  // 从配置文件构造
	SensorConfig() {};          // 默认构造函数

public:
	// === 相机配置 ===
	gv::CameraConfig cam;       // 相机内参、外参、IPM参数等

	// === 位姿平滑参数 ===
	int pose_smooth_window = 20;      // 位姿平滑窗口大小
	bool need_smooth = true;          // 是否需要位姿平滑
	double large_slope_thresold = 1.5; // 大坡度变化阈值(度)
	double t_start;                   // 处理开始时间
	double t_end;                     // 处理结束时间

	// === 道路标记过滤参数 ===
	int patch_min_size = 50;                    // 最小patch像素数量

	// 虚线参数
	double patch_dashed_min_h = 1.35;           // 虚线最小高度(米)
	double patch_dashed_max_h = 10.0;           // 虚线最大高度(米)
	double patch_dashed_max_dist = 12.0;        // 虚线最大检测距离(米)

	// 引导标记参数
	double patch_guide_min_h = 0.0;             // 引导标记最小高度(米)
	double patch_guide_max_h = 1000.0;          // 引导标记最大高度(米)
	double patch_guide_max_dist = 20.0;         // 引导标记最大检测距离(米)

	// 实线和停止线参数
	double patch_solid_max_dist = 15.0;         // 实线最大检测距离(米)
	double patch_stop_max_dist = 12.0;          // 停止线最大检测距离(米)

	// === 建图参数 ===
	int mapping_step = 10;                      // 每隔多少帧执行一次patch合并
	double mapping_patch_freeze_distance = 10.0;     // patch冻结距离(米)
	double mapping_line_freeze_distance = 10.0;      // 线段冻结距离(米)
	double mapping_line_freeze_max_length = 50.0;    // 线段冻结最大长度(米)

	// 线段聚类参数
	double mapping_line_cluster_max_dist = 1.0;           // 线段聚类最大距离(米)
	double mapping_line_cluster_max_across_dist1 = 1.0;   // 线段聚类横向距离阈值1(米)
	double mapping_line_cluster_max_across_dist2 = 0.4;   // 线段聚类横向距离阈值2(米)
	double mapping_line_cluster_max_theta = 10.0;         // 线段聚类最大角度差(度)

	// === 定位参数 ===
	int localization_max_windowsize = 100;           // 定位最大窗口大小
	int localization_force_last_n_frames = 2;        // 强制使用最近N帧
	int localization_every_n_frames = 5;             // 每隔N帧执行定位
	int localization_min_keyframe_dist = 1.0;        // 关键帧最小距离(米)
	int localization_max_strict_match_dist = 1.0;    // 严格匹配最大距离(米)
	int localization_solid_sample_interval = 3.0;    // 实线采样间隔(米)

	// === 可视化参数 ===
	bool enable_vis_image = true;   // 是否启用图像可视化
	bool enable_vis_3d = true;      // 是否启用3D可视化
};

// === 全局变量声明 ===
extern gviewer viewer;                                    // 3D可视化器实例
extern vector<VisualizedInstance> vis_instances;          // 可视化实例容器
extern std::normal_distribution<double> noise_distribution; // 噪声分布(用于仿真)
extern std::default_random_engine random_engine;          // 随机数生成器

/**
 * @brief 道路标记类型枚举
 * 定义了系统支持的所有道路标记类型
 */
enum PatchType {
	EMPTY = -1,    // 空白/背景
	SOLID = 0,     // 实线车道线
	DASHED = 1,    // 虚线车道线
	GUIDE = 2,     // 引导标记(箭头等)
	ZEBRA = 3,     // 斑马线
	STOP = 4       // 停止线
};

/**
 * @brief 将语义分割的灰度值转换为道路标记类型
 * @param gray 语义分割图像中的像素灰度值
 * @return 对应的道路标记类型
 */
inline PatchType gray2class(int gray)
{
	if (gray == 2) return PatchType::DASHED;      // 灰度值2 -> 虚线
	else if (gray == 3) return PatchType::GUIDE;  // 灰度值3 -> 引导标记
	else if (gray == 4) return PatchType::ZEBRA;  // 灰度值4 -> 斑马线
	else if (gray == 5) return PatchType::STOP;   // 灰度值5 -> 停止线
	else if (gray > 0) return PatchType::SOLID;   // 灰度值1或其他正值 -> 实线
	else return PatchType::EMPTY;                 // 灰度值0 -> 空白
}

/**
 * @brief 将道路标记类型转换为字符串
 * @param PatchType 道路标记类型
 * @return 对应的字符串描述
 */
inline string PatchType2str(PatchType PatchType)
{
	if (PatchType == PatchType::DASHED) return "dashed";
	else if (PatchType == PatchType::GUIDE) return "guide";
	else if (PatchType == PatchType::ZEBRA) return "zebra";
	else if (PatchType == PatchType::SOLID) return "solid";
	else if (PatchType == PatchType::STOP) return "stop";
	else if (PatchType == PatchType::EMPTY) return "empty";
	else return "unknown";
}


/**
 * @brief 道路标记实例类
 * 表示一个道路标记实例，包含其几何信息、类别信息和状态信息
 */
class RoadInstancePatch
{
public:
	static long long next_id;           // 全局ID计数器
	EIGEN_MAKE_ALIGNED_OPERATOR_NEW     // Eigen内存对齐宏

public:
	// === ID和类别信息 ===
	long long id;                       // 实例唯一ID
	PatchType road_class;               // 道路标记类型
	map<PatchType, int> road_class_count; // 各类型像素计数(用于确定最终类型)

	// === 状态标志 ===
	bool line_valid;                    // 线段是否有效(对线状标记)
	bool merged = false;                // 是否已被合并
	bool frozen = false;                // 是否已冻结(不再更新)
	bool valid_add_to_map = false;      // 是否有效可添加到地图
	bool out_range = false;             // 是否超出有效范围

	long long frame_id;                 // 所属帧ID

	// 关联的帧ID列表，与RoadInstancePatchMap::timestamps和queued_poses对应
	// 对于patch类实例 -> linked_frames[0]
	// 对于line类实例  -> linked_frames[i], i = 0, ..., line_points_metric.size()
	vector<vector<long long>> linked_frames;

	// === 边界框相关参数 ===
	// patch类实例的主要参数
	// 边界框四个顶点的排列方式:
	//    p3----p2
	//    |      |
	//    |      |
	//    p0 --- p1
	//
	Eigen::Vector3d b_point[4];        // 图像坐标系下的边界框顶点
	Eigen::Vector3d b_point_metric[4]; // 车体/地图坐标系下的边界框顶点
	double b_unc_dist[4];              // 各边界的距离不确定性

	// === 线段相关参数 ===
	// line类实例的主要参数
	Eigen::VectorXd line_koef;         // 线段拟合系数(多项式系数)
	vector<Eigen::Vector3d, Eigen::aligned_allocator<Eigen::Vector3d>> line_points;        // 图像坐标系下的线段点
	vector<Eigen::Vector3d, Eigen::aligned_allocator<Eigen::Vector3d>> line_points_metric; // 车体/地图坐标系下的线段点
	vector<Eigen::Matrix3d, Eigen::aligned_allocator<Eigen::Matrix3d>> line_points_uncertainty; // 线段点的不确定性矩阵

	// === 原始点云相关参数 ===
	// 通用属性
	// 1) 图像坐标系
	double top, left, width, height;   // 包围盒参数
	Eigen::Vector3d mean;              // 质心
	Eigen::Matrix3d cov;               // 协方差矩阵
	Eigen::Vector3d direction;         // 主方向向量
	double eigen_value[3];             // 特征值(用于判断形状)
	vector<Vector3d, Eigen::aligned_allocator<Eigen::Vector3d>> points; // 所有像素点

	// 2) 车体/地图坐标系
	Eigen::Vector3d mean_metric;       // 3D空间中的质心
	vector<Vector3d, Eigen::aligned_allocator<Eigen::Vector3d>> points_metric; // 3D空间中的点云
	Eigen::Matrix3d mean_uncertainty;  // 质心的不确定性矩阵
	double percept_distance = 10000;   // 感知距离(米)


public:
	/**
	 * @brief 构造函数
	 * 初始化实例ID和边界框顶点
	 */
	RoadInstancePatch()
	{
		id = next_id++;                    // 分配唯一ID
		for (int ii = 0; ii < 4; ii++)
		{
			b_point[ii].setZero();         // 初始化图像坐标系边界框顶点
			b_point_metric[ii].setZero();  // 初始化3D坐标系边界框顶点
		}
	}

	/**
	 * @brief 获取边界框高度
	 * @return 边界框高度(米)
	 */
	double h() const;

	/**
	 * @brief 获取边界框宽度
	 * @return 边界框宽度(米)
	 */
	double w() const;

	/**
	 * @brief 获取边界框/线段的方向向量
	 * @return 单位方向向量
	 */
	Eigen::Vector3d d() const;

};

/**
 * @brief 道路标记帧类
 * 表示某一时刻检测到的所有道路标记实例，包含位姿信息
 */
class RoadInstancePatchFrame
{
public:
	static long long next_id;           // 全局帧ID计数器
	EIGEN_MAKE_ALIGNED_OPERATOR_NEW     // Eigen内存对齐宏

public:
	long long id;                       // 帧唯一ID
	double time;                        // 时间戳
	Eigen::Matrix3d R;                  // 旋转矩阵 R^world_body (世界坐标系到车体坐标系)
	Eigen::Vector3d t;                  // 平移向量 t^world_body (世界坐标系下的车体位置)
	map<PatchType, vector<shared_ptr<RoadInstancePatch>>> patches; // 按类型分组的道路标记实例

public:
	/**
	 * @brief 构造函数
	 * 初始化帧ID
	 */
	RoadInstancePatchFrame()
	{
		id = next_id++;
	}

	/**
	 * @brief 计算道路标记实例的3D度量属性
	 * 将图像坐标系下的信息转换到车体坐标系
	 * @param config 传感器配置
	 * @param imp IPM处理器
	 * @return 成功返回0
	 */
	int generateMetricPatches(const SensorConfig &config, const gv::IPMProcesser &ipm);
};

/**
 * @brief 道路标记地图类
 * 管理所有道路标记实例，提供建图、匹配、保存/加载等功能
 */
class RoadInstancePatchMap
{
public:
	EIGEN_MAKE_ALIGNED_OPERATOR_NEW     // Eigen内存对齐宏

	map<PatchType, vector<shared_ptr<RoadInstancePatch>>> patches; // 按类型分组的道路标记实例
	Eigen::Vector3d ref;                // 地图参考点(用于坐标转换)

	map<long long, pair<Matrix3d, Vector3d>> queued_poses; // 缓存的位姿信息 <帧ID, <旋转矩阵, 平移向量>>
	map<long long, double> timestamps;  // 时间戳信息 <帧ID, 时间戳>

public:
	/**
	 * @brief 向地图中添加一帧道路标记数据
	 * @param frame 道路标记帧
	 * @return 成功返回0
	 */
	int addFrame(const RoadInstancePatchFrame & frame);

	/**
	 * @brief 合并地图中的道路标记实例
	 * @param config 传感器配置
	 * @param mode 模式: 0=增量建图; 1=地图合并/检查
	 * @param Rwv 车辆到世界坐标系旋转矩阵(mode 0时使用)
	 * @param twv 车辆到世界坐标系平移向量(mode 0时使用)
	 * @return 成功返回0
	 *
	 * 对于mode 0: Rwv和twv用于确定活跃实例并冻结旧实例
	 * 对于mode 1: Rwv和twv无用，使用默认值即可
	 */
	int mergePatches(const SensorConfig &config, const int mode, const Eigen::Matrix3d &Rwv = Eigen::Matrix3d::Identity(), const Eigen::Vector3d & twv = Eigen::Vector3d::Zero());

	/**
	 * @brief 简单地将两个地图的实例堆叠
	 * 实例不会被合并
	 * @param road_map_other 另一个道路标记地图
	 * @return 成功返回0
	 */
	int mergeMap(const RoadInstancePatchMap& road_map_other);

	/**
	 * @brief 清空地图
	 * @return 成功返回0
	 */
	int clearMap();

	/**
	 * @brief 解冻所有实例(用于进一步合并)
	 * @return 成功返回0
	 */
	int unfreeze();

	/**
	 * @brief 完整性检查
	 * @return 成功返回0
	 */
	int cleanMap();

	/**
	 * @brief 保存地图到二进制文件
	 * 注意：只保存度量尺度的属性
	 * @param filename 文件名
	 * @return 成功返回0
	 */
	int saveMapToFileBinaryRaw(string filename);

	/**
	 * @brief 从二进制文件加载地图
	 * @param filename 文件名
	 * @return 成功返回0
	 */
	int loadMapFromFileBinaryRaw(string filename);

	/**
	 * @brief 构建KD树用于地图匹配
	 * @return 成功返回0
	 */
	int buildKDTree();

	/**
	 * @brief 实例级最近邻匹配
	 * @param config 传感器配置
	 * @param frame 当前帧
	 * @param mode 匹配模式: 0=正常, 1=严格
	 * @return 匹配结果 <类型, <帧实例索引, 地图实例索引>对列表>
	 */
	map<PatchType, vector<pair<int, int>>> mapMatch(const SensorConfig &config,
		RoadInstancePatchFrame &frame, int mode = 0);

	/**
	 * @brief 线段匹配(用于构建测量)
	 * @param config 传感器配置
	 * @param frame 当前帧
	 * @param road_class 道路标记类型
	 * @param frame_line_count 帧中线段数量
	 * @param map_line_count 地图中线段数量
	 * @param mode 匹配模式
	 * @return 匹配对列表
	 */
	vector<pair<int, int>> getLineMatch(const SensorConfig &config, RoadInstancePatchFrame &frame, PatchType road_class,
		int frame_line_count, int map_line_count, int mode =0);

	/**
	 * @brief 基于关联帧对地图元素进行地理配准
	 * 注意：之后需要调用mergePatches函数保持一致性
	 * @param new_traj 新的轨迹
	 * @param lines_vis 可视化线段
	 * @return 成功返回0
	 */
	int geoRegister(const Trajectory& new_traj,
		vector<VisualizedInstance>& lines_vis);

public:
	map<long long, double> ignore_frame_ids; // 忽略的帧ID及其距离阈值
private:

};


/**
 * @brief					Merge the line instance cluster.
 * @param lines_in			The raw line cluser.
 * @param line_est			The merged line instance.
 *
 * @return					Success flag.
 */
extern int LineCluster2SingleLine(const PatchType road_class, const vector<shared_ptr<RoadInstancePatch>>& lines_in, shared_ptr<RoadInstancePatch>& line_est, Eigen::Matrix3d Rwv = Eigen::Matrix3d::Identity());

/**
 * @brief					Use the semantic IPM image to generate the road marking instances.
 * @param config			The raw line cluser.
 * @param ipm				IPM processor with up-to-date camera-ground parameters.
 * @param ipm_raw			RGB IPM image.
 * @param ipm_class			Semantic IPM image. (label = 0,1,2,3,4,5, other)
 *
 * @return					Success flag.
 */
extern RoadInstancePatchFrame generateInstancePatch(const SensorConfig& config, const gv::IPMProcesser& ipm, const cv::Mat& ipm_raw, const cv::Mat& ipm_class);

/**
 * @brief					Calculate the uncertainty of the element on the IPM image based on the pixel coordinates (uI, vI).
 * @param uI, vI			Pixel coordinates on the IPM image.
 *
 * @return					Uncertainty matrix.
 */
extern Matrix3d calcUncertainty(const gv::IPMProcesser& ipm, double uI, double vI);

extern int PointCloud2Curve2D(const vector<Vector3d, Eigen::aligned_allocator<Eigen::Vector3d>>& points, int dim, VectorXd& K);

extern int LabelClustering(const cv::Mat& ipm_class, cv::Mat& ipm_label, cv::Mat& stats, cv::Mat& centroids);