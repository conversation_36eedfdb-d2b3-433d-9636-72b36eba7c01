#!/usr/bin/env python3
"""
RoadLib Binary Map to ArcGIS Converter
将RoadLib生成的二进制地图文件转换为ArcGIS软件可以打开的格式

支持的输出格式:
- Shapefile (.shp): 矢量格式，支持点、线、面
- GeoJSON (.geojson): 开放标准的地理数据格式
- CSV (.csv): 带坐标的表格格式，可在ArcGIS中显示XY数据
- KML (.kml): Google Earth格式，ArcGIS也支持

使用方法:
python convert_roadlib_to_arcgis.py input.bin output_format [output_file]

参数:
- input.bin: RoadLib生成的二进制地图文件
- output_format: 输出格式 (shp/geojson/csv/kml)
- output_file: 可选，输出文件名（不指定则自动生成）
"""

import struct
import sys
import os
import json
import math
from typing import List, Dict, Tuple, Optional

class RoadPatch:
    """道路标记实例类"""
    def __init__(self):
        self.next_id = 0
        self.id = 0
        self.road_class = 0  # 0=SOLID, 1=DASHED, 2=GUIDE, 3=ZEBRA, 4=STOP
        self.line_valid = False
        self.frozen = False
        self.merged = False
        self.valid_add_to_map = False
        self.mean_metric = []
        self.b_points = [[], [], [], []]  # 4个边界点
        self.b_unc = [0.0, 0.0, 0.0, 0.0]
        self.line_points = []  # 线段点
        self.mean_uncertainty = []
        self.line_points_uncertainty = []
        self.percept_distance = 0.0

class RoadLibToArcGISConverter:
    """RoadLib二进制文件转ArcGIS格式转换器"""
    
    CLASS_NAMES = {
        0: "SOLID",
        1: "DASHED", 
        2: "GUIDE",
        3: "ZEBRA",
        4: "STOP"
    }
    
    def __init__(self, binary_file: str):
        self.binary_file = binary_file
        self.patches: Dict[int, List[RoadPatch]] = {}
        self.pos_ref = None
        
    def load_binary_map(self):
        """加载二进制地图文件"""
        print(f"[INFO] 正在加载二进制地图文件: {self.binary_file}")
        
        with open(self.binary_file, 'rb') as fp:
            buff = fp.read()
        
        offset = 0
        
        # 读取参考位置 (3个double)
        self.pos_ref = struct.unpack_from('ddd', buff, offset)
        offset += 8 * 3
        print(f"[INFO] 参考位置 (XYZ): {self.pos_ref}")
        
        # 读取类别数量
        class_count = struct.unpack_from('i', buff, offset)[0]
        offset += 8 * 1
        print(f"[INFO] 道路标记类别数量: {class_count}")
        
        # 读取每个类别的数据
        for iclass in range(class_count):
            road_class = struct.unpack_from('i', buff, offset)[0]
            offset += 4 * 1
            
            patch_count = struct.unpack_from('i', buff, offset)[0]
            offset += 8 * 1
            
            print(f"[INFO] 类别 {road_class} ({self.CLASS_NAMES.get(road_class, 'UNKNOWN')}): {patch_count} 个实例")
            
            self.patches[road_class] = []
            
            # 读取每个patch的数据
            for ipatch in range(patch_count):
                patch = RoadPatch()
                
                # 读取基本信息
                patch.next_id = struct.unpack_from('q', buff, offset)[0]; offset += 8
                patch.id = struct.unpack_from('q', buff, offset)[0]; offset += 8
                patch.road_class = struct.unpack_from('i', buff, offset)[0]; offset += 4
                patch.line_valid = struct.unpack_from('?', buff, offset)[0]; offset += 1
                patch.frozen = struct.unpack_from('?', buff, offset)[0]; offset += 1
                patch.merged = struct.unpack_from('?', buff, offset)[0]; offset += 1
                patch.valid_add_to_map = struct.unpack_from('?', buff, offset)[0]; offset += 1
                
                # 读取中心点
                patch.mean_metric = list(struct.unpack_from('fff', buff, offset))
                offset += 4 * 3
                
                # 读取4个边界点
                for i in range(4):
                    patch.b_points[i] = list(struct.unpack_from('fff', buff, offset))
                    offset += 4 * 3
                
                # 读取边界不确定性
                for i in range(4):
                    patch.b_unc[i] = struct.unpack_from('f', buff, offset)[0]
                    offset += 4
                
                # 读取线段点
                line_point_count = struct.unpack_from('i', buff, offset)[0]
                offset += 8
                
                for ilp in range(line_point_count):
                    lp = list(struct.unpack_from('fff', buff, offset))
                    patch.line_points.append(lp)
                    offset += 4 * 3
                
                # 读取均值不确定性矩阵 (3x3)
                patch.mean_uncertainty = list(struct.unpack_from('fffffffff', buff, offset))
                offset += 4 * 9
                
                # 读取线段点不确定性
                line_uncertainty_count = struct.unpack_from('i', buff, offset)[0]
                offset += 8
                
                for ilp in range(line_uncertainty_count):
                    lp_uncertainty = list(struct.unpack_from('fffffffff', buff, offset))
                    patch.line_points_uncertainty.append(lp_uncertainty)
                    offset += 4 * 9
                
                # 读取感知距离
                patch.percept_distance = struct.unpack_from('d', buff, offset)[0]
                offset += 8
                
                self.patches[road_class].append(patch)
        
        print(f"[INFO] 地图加载完成")
    
    def local_to_global_coords(self, local_coords):
        """将局部坐标转换为全球坐标（简化版本）"""
        # 这里假设局部坐标系是ENU（东-北-上），参考点是全球坐标
        # 实际应用中可能需要更复杂的坐标转换
        if len(local_coords) >= 2:
            global_x = self.pos_ref[0] + local_coords[0]  # 经度方向
            global_y = self.pos_ref[1] + local_coords[1]  # 纬度方向
            global_z = self.pos_ref[2] + (local_coords[2] if len(local_coords) > 2 else 0)
            return [global_x, global_y, global_z]
        return local_coords
    
    def export_to_csv(self, output_file: str):
        """导出为CSV格式，适合ArcGIS的Display XY Data功能"""
        print(f"[INFO] 正在导出CSV格式到: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            # CSV头部 - ArcGIS友好的列名
            f.write("OBJECTID,PATCH_ID,ROAD_CLASS,CLASS_NAME,POINT_TYPE,X,Y,Z,UNCERTAINTY,LINE_VALID,PERCEPT_DIST\n")
            
            object_id = 1
            for road_class, patches in self.patches.items():
                class_name = self.CLASS_NAMES.get(road_class, 'UNKNOWN')
                
                for patch in patches:
                    # 中心点
                    coords = self.local_to_global_coords(patch.mean_metric)
                    f.write(f"{object_id},{patch.id},{road_class},{class_name},CENTER,")
                    f.write(f"{coords[0]:.8f},{coords[1]:.8f},{coords[2]:.3f},0.0,")
                    f.write(f"{patch.line_valid},{patch.percept_distance:.3f}\n")
                    object_id += 1
                    
                    # 边界点
                    for i, bp in enumerate(patch.b_points):
                        if len(bp) == 3:
                            coords = self.local_to_global_coords(bp)
                            unc = patch.b_unc[i] if i < len(patch.b_unc) else 0.0
                            f.write(f"{object_id},{patch.id},{road_class},{class_name},BOUNDARY_{i},")
                            f.write(f"{coords[0]:.8f},{coords[1]:.8f},{coords[2]:.3f},{unc:.6f},")
                            f.write(f"{patch.line_valid},{patch.percept_distance:.3f}\n")
                            object_id += 1
                    
                    # 线段点
                    for i, lp in enumerate(patch.line_points):
                        if len(lp) == 3:
                            coords = self.local_to_global_coords(lp)
                            f.write(f"{object_id},{patch.id},{road_class},{class_name},LINE_{i},")
                            f.write(f"{coords[0]:.8f},{coords[1]:.8f},{coords[2]:.3f},0.0,")
                            f.write(f"{patch.line_valid},{patch.percept_distance:.3f}\n")
                            object_id += 1
        
        print(f"[INFO] CSV文件导出完成，共 {object_id-1} 个点")
        print(f"[INFO] 在ArcGIS中使用: File -> Add Data -> Add XY Data")
    
    def export_to_geojson(self, output_file: str):
        """导出为GeoJSON格式"""
        print(f"[INFO] 正在导出GeoJSON格式到: {output_file}")
        
        features = []
        
        for road_class, patches in self.patches.items():
            class_name = self.CLASS_NAMES.get(road_class, 'UNKNOWN')
            
            for patch in patches:
                # 创建点要素（中心点）
                center_coords = self.local_to_global_coords(patch.mean_metric)
                point_feature = {
                    "type": "Feature",
                    "geometry": {
                        "type": "Point",
                        "coordinates": [center_coords[0], center_coords[1]]
                    },
                    "properties": {
                        "PATCH_ID": patch.id,
                        "ROAD_CLASS": road_class,
                        "CLASS_NAME": class_name,
                        "POINT_TYPE": "CENTER",
                        "LINE_VALID": patch.line_valid,
                        "PERCEPT_DIST": patch.percept_distance,
                        "ELEVATION": center_coords[2] if len(center_coords) > 2 else 0
                    }
                }
                features.append(point_feature)
                
                # 创建线要素（如果有线段点）
                if len(patch.line_points) >= 2:
                    line_coords = []
                    for lp in patch.line_points:
                        if len(lp) >= 2:
                            coords = self.local_to_global_coords(lp)
                            line_coords.append([coords[0], coords[1]])
                    
                    if len(line_coords) >= 2:
                        line_feature = {
                            "type": "Feature",
                            "geometry": {
                                "type": "LineString",
                                "coordinates": line_coords
                            },
                            "properties": {
                                "PATCH_ID": patch.id,
                                "ROAD_CLASS": road_class,
                                "CLASS_NAME": class_name,
                                "GEOM_TYPE": "LINE",
                                "LINE_VALID": patch.line_valid,
                                "PERCEPT_DIST": patch.percept_distance,
                                "POINT_COUNT": len(line_coords)
                            }
                        }
                        features.append(line_feature)
                
                # 创建面要素（如果有4个边界点）
                if len(patch.b_points) == 4 and all(len(bp) >= 2 for bp in patch.b_points):
                    polygon_coords = []
                    for bp in patch.b_points:
                        coords = self.local_to_global_coords(bp)
                        polygon_coords.append([coords[0], coords[1]])
                    # 闭合多边形
                    polygon_coords.append(polygon_coords[0])
                    
                    polygon_feature = {
                        "type": "Feature",
                        "geometry": {
                            "type": "Polygon",
                            "coordinates": [polygon_coords]
                        },
                        "properties": {
                            "PATCH_ID": patch.id,
                            "ROAD_CLASS": road_class,
                            "CLASS_NAME": class_name,
                            "GEOM_TYPE": "POLYGON",
                            "LINE_VALID": patch.line_valid,
                            "PERCEPT_DIST": patch.percept_distance
                        }
                    }
                    features.append(polygon_feature)
        
        # 创建GeoJSON对象
        geojson = {
            "type": "FeatureCollection",
            "crs": {
                "type": "name",
                "properties": {
                    "name": "EPSG:4326"  # WGS84坐标系
                }
            },
            "features": features
        }
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(geojson, f, indent=2, ensure_ascii=False)
        
        print(f"[INFO] GeoJSON文件导出完成，共 {len(features)} 个要素")
        print(f"[INFO] 在ArcGIS中直接拖拽打开或使用Add Data功能")

    def export_to_kml(self, output_file: str):
        """导出为KML格式"""
        print(f"[INFO] 正在导出KML格式到: {output_file}")

        # KML文件头
        kml_header = '<?xml version="1.0" encoding="UTF-8"?>\n'
        kml_header += '<kml xmlns="http://www.opengis.net/kml/2.2">\n'
        kml_header += '<Document>\n'
        kml_header += f'<name>RoadLib Map: {os.path.basename(self.binary_file)}</name>\n'
        kml_header += '<description>Exported from RoadLib binary map file</description>\n'

        # 为每种道路类型创建样式
        kml_styles = ''
        style_colors = {
            0: 'ff00ff00',  # SOLID - 绿色 (ABGR格式)
            1: 'ff00a5ff',  # DASHED - 橙色
            2: 'ffff0000',  # GUIDE - 蓝色
            3: 'ff00ffff',  # ZEBRA - 黄色
            4: 'ff0000ff',  # STOP - 红色
        }

        for road_class, color in style_colors.items():
            class_name = self.CLASS_NAMES.get(road_class, 'UNKNOWN')
            kml_styles += f'<Style id="style_{road_class}">\n'
            kml_styles += '  <IconStyle>\n'
            kml_styles += f'    <color>{color}</color>\n'
            kml_styles += '    <scale>0.8</scale>\n'
            kml_styles += '  </IconStyle>\n'
            kml_styles += '  <LineStyle>\n'
            kml_styles += f'    <color>{color}</color>\n'
            kml_styles += '    <width>3</width>\n'
            kml_styles += '  </LineStyle>\n'
            kml_styles += '  <PolyStyle>\n'
            kml_styles += f'    <color>{color}80</color>\n'  # 半透明
            kml_styles += '    <outline>1</outline>\n'
            kml_styles += '  </PolyStyle>\n'
            kml_styles += '</Style>\n'

        # KML内容
        kml_content = ''

        # 为每个类别创建一个文件夹
        for road_class, patches in self.patches.items():
            class_name = self.CLASS_NAMES.get(road_class, 'UNKNOWN')

            kml_content += f'<Folder>\n'
            kml_content += f'<name>{class_name} ({len(patches)})</name>\n'

            for patch in patches:
                # 创建Placemark
                kml_content += f'<Placemark>\n'
                kml_content += f'<name>ID: {patch.id}</name>\n'
                kml_content += f'<styleUrl>#style_{road_class}</styleUrl>\n'
                kml_content += '<description>\n'
                kml_content += f'<![CDATA[\n'
                kml_content += f'<b>ID:</b> {patch.id}<br/>\n'
                kml_content += f'<b>Class:</b> {class_name}<br/>\n'
                kml_content += f'<b>Line Valid:</b> {patch.line_valid}<br/>\n'
                kml_content += f'<b>Percept Distance:</b> {patch.percept_distance:.2f}m<br/>\n'
                kml_content += f']]>\n'
                kml_content += '</description>\n'

                # 根据不同类型的几何体创建不同的KML元素
                if len(patch.line_points) >= 2:
                    # 创建LineString
                    kml_content += '<LineString>\n'
                    kml_content += '<tessellate>1</tessellate>\n'
                    kml_content += '<altitudeMode>relativeToGround</altitudeMode>\n'
                    kml_content += '<coordinates>\n'

                    for lp in patch.line_points:
                        if len(lp) >= 2:
                            coords = self.local_to_global_coords(lp)
                            kml_content += f'{coords[0]},{coords[1]},{coords[2]}\n'

                    kml_content += '</coordinates>\n'
                    kml_content += '</LineString>\n'
                elif len(patch.b_points) == 4 and all(len(bp) >= 2 for bp in patch.b_points):
                    # 创建Polygon
                    kml_content += '<Polygon>\n'
                    kml_content += '<tessellate>1</tessellate>\n'
                    kml_content += '<altitudeMode>relativeToGround</altitudeMode>\n'
                    kml_content += '<outerBoundaryIs>\n'
                    kml_content += '<LinearRing>\n'
                    kml_content += '<coordinates>\n'

                    for bp in patch.b_points:
                        coords = self.local_to_global_coords(bp)
                        kml_content += f'{coords[0]},{coords[1]},{coords[2]}\n'

                    # 闭合多边形
                    coords = self.local_to_global_coords(patch.b_points[0])
                    kml_content += f'{coords[0]},{coords[1]},{coords[2]}\n'

                    kml_content += '</coordinates>\n'
                    kml_content += '</LinearRing>\n'
                    kml_content += '</outerBoundaryIs>\n'
                    kml_content += '</Polygon>\n'
                else:
                    # 创建Point
                    coords = self.local_to_global_coords(patch.mean_metric)
                    kml_content += '<Point>\n'
                    kml_content += '<altitudeMode>relativeToGround</altitudeMode>\n'
                    kml_content += f'<coordinates>{coords[0]},{coords[1]},{coords[2]}</coordinates>\n'
                    kml_content += '</Point>\n'

                kml_content += '</Placemark>\n'

            kml_content += '</Folder>\n'

        # KML文件尾
        kml_footer = '</Document>\n</kml>'

        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(kml_header + kml_styles + kml_content + kml_footer)

        print(f"[INFO] KML文件导出完成")
        print(f"[INFO] 在ArcGIS中使用Add Data功能导入KML文件")

    def export_to_shapefile(self, output_file: str):
        """导出为Shapefile格式"""
        try:
            import shapefile
        except ImportError:
            print("[ERROR] 导出Shapefile需要安装pyshp库")
            print("[ERROR] 请运行: pip install pyshp")
            return

        print(f"[INFO] 正在导出Shapefile格式到: {output_file}")

        # 创建输出目录
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 移除扩展名，shapefile库会自动添加
        if output_file.lower().endswith('.shp'):
            output_file = output_file[:-4]

        # 创建点shapefile
        point_shp = shapefile.Writer(output_file + '_points', shapefile.POINT)
        point_shp.field('PATCH_ID', 'N', 10, 0)
        point_shp.field('ROAD_CLASS', 'N', 2, 0)
        point_shp.field('CLASS_NAME', 'C', 10)
        point_shp.field('POINT_TYPE', 'C', 10)
        point_shp.field('LINE_VALID', 'L')
        point_shp.field('PERCEPT_DST', 'N', 10, 3)
        point_shp.field('X', 'N', 20, 8)
        point_shp.field('Y', 'N', 20, 8)
        point_shp.field('Z', 'N', 10, 3)

        # 创建线shapefile
        line_shp = shapefile.Writer(output_file + '_lines', shapefile.POLYLINE)
        line_shp.field('PATCH_ID', 'N', 10, 0)
        line_shp.field('ROAD_CLASS', 'N', 2, 0)
        line_shp.field('CLASS_NAME', 'C', 10)
        line_shp.field('LINE_VALID', 'L')
        line_shp.field('PERCEPT_DST', 'N', 10, 3)
        line_shp.field('POINT_COUNT', 'N', 5, 0)

        # 创建面shapefile
        poly_shp = shapefile.Writer(output_file + '_polygons', shapefile.POLYGON)
        poly_shp.field('PATCH_ID', 'N', 10, 0)
        poly_shp.field('ROAD_CLASS', 'N', 2, 0)
        poly_shp.field('CLASS_NAME', 'C', 10)
        poly_shp.field('LINE_VALID', 'L')
        poly_shp.field('PERCEPT_DST', 'N', 10, 3)

        point_count = 0
        line_count = 0
        poly_count = 0

        for road_class, patches in self.patches.items():
            class_name = self.CLASS_NAMES.get(road_class, 'UNKNOWN')

            for patch in patches:
                # 添加中心点
                coords = self.local_to_global_coords(patch.mean_metric)
                point_shp.point(coords[0], coords[1], coords[2] if len(coords) > 2 else 0)
                point_shp.record(
                    patch.id,
                    road_class,
                    class_name,
                    'CENTER',
                    patch.line_valid,
                    patch.percept_distance,
                    coords[0],
                    coords[1],
                    coords[2] if len(coords) > 2 else 0
                )
                point_count += 1

                # 添加边界点
                for i, bp in enumerate(patch.b_points):
                    if len(bp) >= 2:
                        coords = self.local_to_global_coords(bp)
                        point_shp.point(coords[0], coords[1], coords[2] if len(coords) > 2 else 0)
                        point_shp.record(
                            patch.id,
                            road_class,
                            class_name,
                            f'BOUNDARY_{i}',
                            patch.line_valid,
                            patch.percept_distance,
                            coords[0],
                            coords[1],
                            coords[2] if len(coords) > 2 else 0
                        )
                        point_count += 1

                # 添加线段
                if len(patch.line_points) >= 2:
                    line_points = []
                    for lp in patch.line_points:
                        if len(lp) >= 2:
                            coords = self.local_to_global_coords(lp)
                            line_points.append([coords[0], coords[1]])

                    if len(line_points) >= 2:
                        line_shp.line([line_points])
                        line_shp.record(
                            patch.id,
                            road_class,
                            class_name,
                            patch.line_valid,
                            patch.percept_distance,
                            len(line_points)
                        )
                        line_count += 1

                # 添加多边形
                if len(patch.b_points) == 4 and all(len(bp) >= 2 for bp in patch.b_points):
                    poly_points = []
                    for bp in patch.b_points:
                        coords = self.local_to_global_coords(bp)
                        poly_points.append([coords[0], coords[1]])

                    # 闭合多边形
                    poly_points.append(poly_points[0])

                    poly_shp.poly([poly_points])
                    poly_shp.record(
                        patch.id,
                        road_class,
                        class_name,
                        patch.line_valid,
                        patch.percept_distance
                    )
                    poly_count += 1

        # 关闭shapefile
        point_shp.close()
        line_shp.close()
        poly_shp.close()

        print(f"[INFO] Shapefile导出完成")
        print(f"[INFO] 点要素: {point_count}个 -> {output_file}_points.shp")
        print(f"[INFO] 线要素: {line_count}个 -> {output_file}_lines.shp")
        print(f"[INFO] 面要素: {poly_count}个 -> {output_file}_polygons.shp")
        print(f"[INFO] 在ArcGIS中使用Add Data功能导入这些Shapefile文件")

def main():
    """主函数"""
    # 方法1：直接在代码中指定文件路径（推荐用于固定文件）
    # 取消下面的注释并修改路径，然后注释掉命令行参数部分
    # input_file = r"C:\csy\RoadLib-master\map_output_whu_final.bin"
    # output_format = "geojson"  # 可选: shp/geojson/csv/kml
    # output_file = r"C:\csy\RoadLib-master\road_markings.geojson"  # 可选，不指定则自动生成

    # 方法2：使用命令行参数（默认方式）
    if len(sys.argv) < 3:
        print("使用方法:")
        print("python convert_roadlib_to_arcgis.py <input.bin> <output_format> [output_file]")
        print("")
        print("参数:")
        print("  input.bin      - RoadLib生成的二进制地图文件")
        print("  output_format  - 输出格式: shp/geojson/csv/kml")
        print("  output_file    - 可选，输出文件名（不指定则自动生成）")
        print("")
        print("示例:")
        print("python convert_roadlib_to_arcgis.py map_output_whu_final.bin geojson")
        print("python convert_roadlib_to_arcgis.py map_output_whu_final.bin shp road_map")
        sys.exit(1)

    input_file = sys.argv[1]
    output_format = sys.argv[2].lower()

    # 方法1：如果已经在代码中指定了文件路径，则使用指定的路径
    # 取消下面的注释并修改路径，然后注释掉上面的命令行参数部分
    # 如果已经定义了input_file和output_format变量，则跳过命令行参数处理
    if 'input_file' not in locals():
        input_file = r"C:\csy\RoadLib-master\map_output_whu_final.bin"  # 默认输入文件

    if 'output_format' not in locals():
        output_format = "geojson"  # 默认输出格式

    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"[ERROR] 输入文件不存在: {input_file}")
        sys.exit(1)

    # 检查输出格式是否支持
    supported_formats = ['shp', 'geojson', 'csv', 'kml']
    if output_format not in supported_formats:
        print(f"[ERROR] 不支持的输出格式: {output_format}")
        print(f"支持的格式: {', '.join(supported_formats)}")
        sys.exit(1)

    # 生成输出文件名
    if 'output_file' not in locals():
        if len(sys.argv) >= 4:
            output_file = sys.argv[3]
        else:
            base_name = os.path.splitext(input_file)[0]
            if output_format == 'shp':
                output_file = f"{base_name}"  # shapefile会自动添加扩展名
            else:
                output_file = f"{base_name}.{output_format}"

    print(f"[INFO] 输入文件: {input_file}")
    print(f"[INFO] 输出格式: {output_format}")
    print(f"[INFO] 输出文件: {output_file}")
    print("")

    # 创建转换器并加载数据
    converter = RoadLibToArcGISConverter(input_file)
    converter.load_binary_map()

    # 根据格式导出
    if output_format == 'shp':
        converter.export_to_shapefile(output_file)
    elif output_format == 'geojson':
        converter.export_to_geojson(output_file)
    elif output_format == 'csv':
        converter.export_to_csv(output_file)
    elif output_format == 'kml':
        converter.export_to_kml(output_file)

    print(f"[INFO] 转换完成！")
    print(f"[INFO] 现在可以在ArcGIS软件中打开文件")

if __name__ == "__main__":
    main()
