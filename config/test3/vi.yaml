%YAML:1.0

# 相机标定文件路径 - 包含相机内参(焦距、主点、畸变系数)
cam0_calib: "cam0_pinhole.yaml"

# 数据处理时间范围 - 影响处理哪些帧的数据
t_start: 1752217206.21   # 开始时间戳(秒) - 必须匹配stamp.txt中的时间范围
t_end: 1752217223.52       # 结束时间戳(秒) - 超出范围的帧会被忽略

# 相机-地面几何参数 - 影响IPM变换的准确性，错误设置会导致ROI越界
# cg_alpha: -2     # 相机俯仰角(度) - 相机向下倾斜角度，影响IPM变换
# cg_theta: -1.5    # 相机偏航角(度) - 相机左右偏转角度，影响道路标记检测
cg_alpha: -2.5     # 相机俯仰角(度) - 相机向下倾斜角度，影响IPM变换
cg_theta: -2.5    # 相机偏航角(度) - 相机左右偏转角度，影响道路标记检测
cg_h: 1.16         # 相机高度(米) - 相机离地面高度，影响距离计算

# IPM(逆透视变换)设置 - 将透视图像转换为鸟瞰图，参数错误会导致程序崩溃
IPM_WIDTH: 300    # IPM图像宽度(像素) - 影响处理范围和计算量
IPM_HEIGHT: 600   # IPM图像高度(像素) - 影响前方观测距离
IPM_RESO: 0.05   # IPM分辨率(米/像素) - 每像素代表的实际距离，影响精度

# 外参矩阵 - 机器人本体坐标系到相机坐标系的变换矩阵
# 影响相机在机器人上的位置和姿态，错误会导致定位偏差
body_T_cam0: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   # data: [ 0.999987, 0.003079, 0.004066, 0.023061,    # [R|t] 旋转和平移
   #        -0.003071, 0.999993,-0.001967, -0.000217,   # 影响坐标系转换
   #         -0.004072,0.001955, -0.99999, -0.00200,    # 必须准确标定
   #         0.00000000,0.00000000,0.00000000,1.00000000] # 齐次坐标
#到着开的
   # data: [ 0.999987, 0.004066, -0.003079, -0.023061,
   #        -0.003071, -0.001967,-0.999993, -0.000217,
   #         -0.004072,-0.002000, -0.001955, -0.00200,
   #         0.00000000,0.00000000,0.00000000,1.00000000]
#武汉大学的外参矩阵
   # data: [ 0.99990521, 0.00735322, 0.01164073, -0.1873279983012331,
   #         -0.01168075, 0.00542324, 0.99991707, 0.17845739160143645,
   #         0.00728948, -0.99995826, 0.00550862, 0.09025052016549336,
   #         0.00000000,0.00000000,0.00000000,1.00000000]
   
   #旋转90度
   # data: [ 0.999987, 0.003079, 0.004066, 0.023061,
   #         0.004072, -0.001955, -0.99990, 0.02,
   #        -0.003071, 0.999993, -0.001967,-0.00217,
   #         0.00000000,0.00000000,0.00000000,1.00000000]
   #gpt给的
   data: [ 0.99985489, -0.00287542, -0.01674602, 0.023061,
          0.01670941,0.00866646,0.99982365, -0.000217,
           -0.00302095,-0.99995806, 0.00861576, -0.00200,
           0.00000000,0.00000000,0.00000000,1.00000000]   
# 道路标记观测过滤阈值 - 影响哪些道路标记会被检测和使用
patch.min_size: 50                    # 最小patch尺寸(像素²) - 过小的标记被过滤
patch.dashed_min_h: 1.35             # 虚线标记最小高度(米) - 影响虚线检测
patch.dashed_max_h: 10.0             # 虚线标记最大高度(米) - 过大的被过滤
patch.dashed_max_dist: 12.0          # 虚线标记最大距离(米) - 超出距离忽略
patch.guide_min_h: 0.0               # 引导标记最小高度(米) - 影响引导线检测
patch.guide_max_h: 1000.0            # 引导标记最大高度(米) - 基本不限制
patch.guide_max_dist: 20.0           # 引导标记最大距离(米) - 影响检测范围
patch.solid_max_dist: 15.0           # 实线标记最大距离(米) - 影响实线检测范围
patch.stop_max_dist: 12.0            # 停止线最大距离(米) - 影响停止线检测

# IMU辅助IPM补偿 - 使用IMU数据补偿车辆姿态变化对IPM的影响
need_smooth: 1                    # 是否启用平滑(0/1) - 影响IPM稳定性
pose_smooth_window: 15            # 姿态平滑窗口大小 - 影响平滑效果和延迟
large_slope_thresold: 1.5         # 大坡度阈值(度) - 超过此值触发特殊处理

# 可视化设置 - 影响程序运行时的显示效果，不影响算法结果
enable_vis_image: 1               # 启用图像可视化(0/1) - 显示处理过程
enable_vis_3d: 1                  # 启用3D可视化(0/1) - 显示3D建图结果

# 建图阶段参数 - 影响地图构建的质量和效率
mapping.step: 10                              # 每隔x步执行patch合并 - 影响建图频率
mapping.patch_freeze_distance: 3.0           # patch冻结距离(米) - 经过后变为非活跃状态
mapping.line_freeze_distance: 10.0           # 线标记冻结距离(米) - 影响线标记生命周期
mapping.line_freeze_max_length: 50.0         # 线标记最大冻结长度(米) - 限制线标记长度
mapping.line_cluster_max_dist: 1.0           # 线标记聚类最大距离(米) - 影响线标记合并
mapping.line_cluster_max_across_dist1: 1.0   # 跨线聚类距离1(米) - 影响平行线检测
mapping.line_cluster_max_across_dist2: 0.4   # 跨线聚类距离2(米) - 更严格的平行线检测
mapping.line_cluster_max_theta: 10.0         # 线标记聚类最大角度差(度) - 影响方向一致性

