#!/usr/bin/env python3
"""
检查GeoJSON文件的有效性
"""

import json
import math
import sys

def check_geojson_file(filename):
    """检查GeoJSON文件"""
    print(f"检查文件: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ JSON格式正确")
        print(f"✓ Features数量: {len(data['features'])}")
        
        # 检查坐标有效性
        invalid_coords = []
        for i, feature in enumerate(data['features']):
            geom = feature['geometry']
            coords = geom['coordinates']
            
            if geom['type'] == 'Point':
                if len(coords) < 2 or any(not math.isfinite(c) for c in coords):
                    invalid_coords.append(i)
            elif geom['type'] == 'LineString':
                if any(len(coord_pair) < 2 or any(not math.isfinite(c) for c in coord_pair) for coord_pair in coords):
                    invalid_coords.append(i)
            elif geom['type'] == 'Polygon':
                if any(len(coord_pair) < 2 or any(not math.isfinite(c) for c in coord_pair) for ring in coords for coord_pair in ring):
                    invalid_coords.append(i)
        
        if invalid_coords:
            print(f"✗ 发现 {len(invalid_coords)} 个无效坐标的features: {invalid_coords[:10]}...")
        else:
            print(f"✓ 所有坐标都有效")
        
        # 检查坐标范围
        all_coords = []
        for feature in data['features']:
            geom = feature['geometry']
            coords = geom['coordinates']
            
            if geom['type'] == 'Point':
                all_coords.append(coords)
            elif geom['type'] == 'LineString':
                all_coords.extend(coords)
            elif geom['type'] == 'Polygon':
                for ring in coords:
                    all_coords.extend(ring)
        
        if all_coords:
            x_coords = [c[0] for c in all_coords]
            y_coords = [c[1] for c in all_coords]
            
            print(f"✓ X坐标范围: {min(x_coords):.2f} 到 {max(x_coords):.2f}")
            print(f"✓ Y坐标范围: {min(y_coords):.2f} 到 {max(y_coords):.2f}")
            
            # 检查坐标是否在合理范围内
            if min(x_coords) < -180 or max(x_coords) > 180:
                print(f"⚠ 警告: X坐标超出经度范围(-180到180)")
            if min(y_coords) < -90 or max(y_coords) > 90:
                print(f"⚠ 警告: Y坐标超出纬度范围(-90到90)")
        
        # 检查属性
        if data['features']:
            sample_props = data['features'][0]['properties']
            print(f"✓ 示例属性: {list(sample_props.keys())}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"✗ JSON解析错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def main():
    # 检查两个文件
    files = [
        'map_output_test1_final.geojson',
        'map_output_whu_final.geojson'
    ]
    
    for filename in files:
        print("=" * 50)
        check_geojson_file(filename)
        print()

if __name__ == "__main__":
    main()
