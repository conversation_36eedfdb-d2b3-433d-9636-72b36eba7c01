%YAML:1.0

cam0_calib: "cam0_pinhole.yaml"

# Timespan.
t_start: 1752216982.81
t_end: 1752217010.81

cg_alpha: 0     # 相机俯仰角(度) - 相机向下倾斜角度，影响IPM变换
cg_theta: 0    # 相机偏航角(度) - 相机左右偏转角度，影响道路标记检测
cg_h: 1.16 

# IPM settings.
IPM_WIDTH: 500
IPM_HEIGHT: 800
IPM_RESO: 0.018

# Extrinsics.
body_T_cam0: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 0.99998, 0.00307, 0.00406, 0.02306,
           -0.00307, 1.0000, -0.00196, -0.00021,
           -0.00407, -0.00195, 0.99998, -0.00200,
           0.00000,0.00000,0.00000,1.00000]

# Thresholds for observation filtering.
patch.min_size: 50                          # general (降低最小尺寸阈值)
patch.dashed_min_h: 1.35                      # "DASHED" type, patch-like
patch.dashed_max_h: 10.0                
patch.dashed_max_dist: 12.0                
patch.guide_min_h: 0.0                        # "GUIDE" type, patch-like
patch.guide_max_h: 1000.0                
patch.guide_max_dist: 20.0                
patch.solid_max_dist: 15.0                    # "SOLID" type, line-like
patch.stop_max_dist: 12.0                     # "STOP" type, line-like

# IMU-aided IPM compensation.
need_smooth: 1
pose_smooth_window: 15
large_slope_thresold: 1.5

# Visualization.
enable_vis_image: 1
enable_vis_3d: 1

# For mapping phase.
mapping.step: 10                              # Perform patch merging every x steps.
mapping.patch_freeze_distance: 3.0           # Turn the patches into inactive status after passing by.
mapping.line_freeze_distance: 10.0              
mapping.line_freeze_max_length: 50.0            
mapping.line_cluster_max_dist: 1.0            # Thresholds for line marking clustering.
mapping.line_cluster_max_across_dist1: 1.0     
mapping.line_cluster_max_across_dist2: 0.4     
mapping.line_cluster_max_theta: 10.0           

