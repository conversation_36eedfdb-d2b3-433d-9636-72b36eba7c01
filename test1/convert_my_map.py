#!/usr/bin/env python3
"""
简化版RoadLib地图转换器 - 专门用于您的地图文件
直接在代码中修改输入和输出文件路径
"""

import struct
import os
import json
from typing import List, Dict

class RoadPatch:
    """道路标记实例类"""
    def __init__(self):
        self.id = 0
        self.road_class = 0
        self.line_valid = False
        self.mean_metric = []
        self.b_points = [[], [], [], []]
        self.line_points = []
        self.percept_distance = 0.0

class SimpleRoadLibConverter:
    """简化的RoadLib转换器"""
    
    CLASS_NAMES = {
        0: "SOLID",
        1: "DASHED", 
        2: "GUIDE",
        3: "ZEBRA",
        4: "STOP"
    }
    
    def __init__(self, binary_file: str):
        self.binary_file = binary_file
        self.patches: Dict[int, List[RoadPatch]] = {}
        self.pos_ref = None
        
    def load_binary_map(self):
        """加载二进制地图文件"""
        print(f"[INFO] 正在加载: {self.binary_file}")
        
        with open(self.binary_file, 'rb') as fp:
            buff = fp.read()
        
        offset = 0
        
        # 读取参考位置
        self.pos_ref = struct.unpack_from('ddd', buff, offset)
        offset += 8 * 3
        print(f"[INFO] 参考位置: {self.pos_ref}")
        
        # 读取类别数量
        class_count = struct.unpack_from('i', buff, offset)[0]
        offset += 8 * 1
        print(f"[INFO] 道路标记类别数量: {class_count}")
        
        # 读取每个类别的数据
        for iclass in range(class_count):
            road_class = struct.unpack_from('i', buff, offset)[0]
            offset += 4 * 1
            
            patch_count = struct.unpack_from('i', buff, offset)[0]
            offset += 8 * 1
            
            print(f"[INFO] {self.CLASS_NAMES.get(road_class, 'UNKNOWN')}: {patch_count} 个")
            
            self.patches[road_class] = []
            
            # 读取每个patch的数据
            for ipatch in range(patch_count):
                patch = RoadPatch()
                
                # 基本信息
                patch.next_id = struct.unpack_from('q', buff, offset)[0]; offset += 8
                patch.id = struct.unpack_from('q', buff, offset)[0]; offset += 8
                patch.road_class = struct.unpack_from('i', buff, offset)[0]; offset += 4
                patch.line_valid = struct.unpack_from('?', buff, offset)[0]; offset += 1
                offset += 3  # 跳过其他布尔值
                
                # 中心点
                patch.mean_metric = list(struct.unpack_from('fff', buff, offset))
                offset += 4 * 3
                
                # 边界点
                for i in range(4):
                    patch.b_points[i] = list(struct.unpack_from('fff', buff, offset))
                    offset += 4 * 3
                
                # 跳过边界不确定性
                offset += 4 * 4
                
                # 线段点
                line_point_count = struct.unpack_from('i', buff, offset)[0]
                offset += 8
                
                for ilp in range(line_point_count):
                    lp = list(struct.unpack_from('fff', buff, offset))
                    patch.line_points.append(lp)
                    offset += 4 * 3
                
                # 跳过不确定性矩阵
                offset += 4 * 9
                
                # 跳过线段点不确定性
                line_uncertainty_count = struct.unpack_from('i', buff, offset)[0]
                offset += 8
                offset += line_uncertainty_count * 4 * 9
                
                # 感知距离
                patch.percept_distance = struct.unpack_from('d', buff, offset)[0]
                offset += 8
                
                self.patches[road_class].append(patch)
        
        print(f"[INFO] 地图加载完成")
    
    def local_to_global_coords(self, local_coords):
        """坐标转换"""
        if len(local_coords) >= 2:
            global_x = self.pos_ref[0] + local_coords[0]
            global_y = self.pos_ref[1] + local_coords[1]
            global_z = self.pos_ref[2] + (local_coords[2] if len(local_coords) > 2 else 0)
            return [global_x, global_y, global_z]
        return local_coords
    
    def export_to_geojson(self, output_file: str):
        """导出为GeoJSON"""
        print(f"[INFO] 导出到: {output_file}")
        
        features = []
        
        for road_class, patches in self.patches.items():
            class_name = self.CLASS_NAMES.get(road_class, 'UNKNOWN')
            
            for patch in patches:
                # 中心点
                center_coords = self.local_to_global_coords(patch.mean_metric)
                point_feature = {
                    "type": "Feature",
                    "geometry": {
                        "type": "Point",
                        "coordinates": [center_coords[0], center_coords[1]]
                    },
                    "properties": {
                        "PATCH_ID": patch.id,
                        "ROAD_CLASS": road_class,
                        "CLASS_NAME": class_name,
                        "POINT_TYPE": "CENTER",
                        "LINE_VALID": patch.line_valid,
                        "PERCEPT_DIST": patch.percept_distance
                    }
                }
                features.append(point_feature)
                
                # 线段
                if len(patch.line_points) >= 2:
                    line_coords = []
                    for lp in patch.line_points:
                        if len(lp) >= 2:
                            coords = self.local_to_global_coords(lp)
                            line_coords.append([coords[0], coords[1]])
                    
                    if len(line_coords) >= 2:
                        line_feature = {
                            "type": "Feature",
                            "geometry": {
                                "type": "LineString",
                                "coordinates": line_coords
                            },
                            "properties": {
                                "PATCH_ID": patch.id,
                                "ROAD_CLASS": road_class,
                                "CLASS_NAME": class_name,
                                "GEOM_TYPE": "LINE"
                            }
                        }
                        features.append(line_feature)
                
                # 多边形
                if len(patch.b_points) == 4 and all(len(bp) >= 2 for bp in patch.b_points):
                    polygon_coords = []
                    for bp in patch.b_points:
                        coords = self.local_to_global_coords(bp)
                        polygon_coords.append([coords[0], coords[1]])
                    polygon_coords.append(polygon_coords[0])  # 闭合
                    
                    polygon_feature = {
                        "type": "Feature",
                        "geometry": {
                            "type": "Polygon",
                            "coordinates": [polygon_coords]
                        },
                        "properties": {
                            "PATCH_ID": patch.id,
                            "ROAD_CLASS": road_class,
                            "CLASS_NAME": class_name,
                            "GEOM_TYPE": "POLYGON"
                        }
                    }
                    features.append(polygon_feature)
        
        # 创建GeoJSON
        geojson = {
            "type": "FeatureCollection",
            "crs": {
                "type": "name",
                "properties": {"name": "EPSG:4326"}
            },
            "features": features
        }
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(geojson, f, indent=2, ensure_ascii=False)
        
        print(f"[INFO] 导出完成，共 {len(features)} 个要素")

def main():
    """主函数 - 在这里修改输入和输出文件路径"""
    
    # ========== 在这里修改文件路径 ==========
    input_file = r"C:\csy\RoadLib-master\map_output_test1_final.bin"
    output_file = r"C:\csy\RoadLib-master\map_output_test1_final.geojson"
    # =====================================
    
    print(f"[INFO] 输入文件: {input_file}")
    print(f"[INFO] 输出文件: {output_file}")
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"[ERROR] 输入文件不存在: {input_file}")
        return
    
    # 创建转换器
    converter = SimpleRoadLibConverter(input_file)
    converter.load_binary_map()
    converter.export_to_geojson(output_file)
    
    print(f"[INFO] 转换完成！现在可以在ArcGIS中打开: {output_file}")

if __name__ == "__main__":
    main()
