#!/bin/bash

# RoadLib 运行脚本
# 使用export设置环境变量的方式

# 设置数据集路径
export DATASET=/home/<USER>/桌面/RoadLib-master/test0

# 进入build目录
cd /home/<USER>/桌面/RoadLib-master/build

# 1. 运行映射程序
echo "正在运行映射程序..."
./demo_mapping ../config/WHU_0412/vi.yaml ${DATASET}/stamp.txt ${DATASET}/cam0 ${DATASET}/semantic ${DATASET}/gt.txt ${DATASET}/odo.txt ../map_output.bin

# 2. 可视化地图
echo "正在可视化地图..."
cd /home/<USER>/桌面/RoadLib-master
python3 scripts/view_map.py map_output.bin

# 3. 运行定位程序
echo "正在运行定位程序..."
cd build
./demo_localization ../config/WHU_0412/vi_loc.yaml ${DATASET}/stamp.txt ${DATASET}/cam0 ${DATASET}/semantic ${DATASET}/gt.txt ${DATASET}/odo.txt ../map_output.bin ../localization_result.txt

# 4. 转换地图格式
echo "正在转换地图格式..."
cd /home/<USER>/桌面/RoadLib-master
python3 convert_my_map.py

echo "所有任务完成！"
