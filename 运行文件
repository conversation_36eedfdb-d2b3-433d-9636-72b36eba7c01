#!/bin/bash

# 设置数据集路径环境变量
export DATASET=/home/<USER>/桌面/RoadLib-master/test0

# 进入build目录
cd /home/<USER>/桌面/RoadLib-master/build

echo "=== 开始运行RoadLib映射程序 ==="
echo "数据集路径: ${DATASET}"
echo "配置文件: ../config/WHU_0412/vi.yaml"
echo "输出地图: ../map_output.bin"
echo ""

# 运行映射程序 (使用export变量)
echo "1. 运行映射程序..."
./demo_mapping ../config/WHU_0412/vi.yaml ${DATASET}/stamp.txt ${DATASET}/cam0 ${DATASET}/semantic ${DATASET}/gt.txt ${DATASET}/odo.txt ../map_output.bin

echo ""
echo "=== 映射完成，开始可视化地图 ==="

# 可视化地图
echo "2. 可视化地图..."
cd /home/<USER>/桌面/RoadLib-master
python3 scripts/view_map.py map_output.bin

echo ""
echo "=== 运行定位程序 ==="

# 运行定位程序
echo "3. 运行定位程序..."
cd build
./demo_localization ../config/WHU_0412/vi_loc.yaml ${DATASET}/stamp.txt ${DATASET}/cam0 ${DATASET}/semantic ${DATASET}/gt.txt ${DATASET}/odo.txt ../map_output.bin ../localization_result.txt

echo ""
echo "=== 转换地图为GeoJSON格式 ==="

# 转换地图格式
echo "4. 转换地图格式..."
cd /home/<USER>/桌面/RoadLib-master
python3 convert_my_map.py

echo ""
echo "=== 所有任务完成！ ==="
echo "生成的文件："
echo "- map_output.bin (二进制地图文件)"
echo "- localization_result.txt (定位结果)"
echo "- map_output_test1_final.geojson (GeoJSON格式地图)"


export DATASET=/home/<USER>/桌面/RoadLib-master/test1 && cd /home/<USER>/桌面/RoadLib-master/build && ./demo_mapping ../config/test1/vi.yaml ${DATASET}/stamp.txt ${DATASET}/cam0 ${DATASET}/semantic ${DATASET}/gt.txt ${DATASET}/odo.txt ../test111.bin

export DATASET=/home/<USER>/桌面/RoadLib-master/test0 && cd /home/<USER>/桌面/RoadLib-master/build && ./demo_mapping ../config/WHU_0412/vi.yaml ${DATASET}/stamp.txt ${DATASET}/cam0 ${DATASET}/semantic ${DATASET}/gt.txt ${DATASET}/odo.txt ../WHU_0412.bin